package com.data.platform.datamind.server.datameta.lineage;

import java.util.Map;

/**
 * 血缘可视化器接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.lineage
 * @description 血缘可视化器，提供血缘关系可视化功能
 * @since 1.8
 */
public interface LineageVisualizer {

    /**
     * 生成表级血缘可视化数据
     *
     * @param tableId 表ID
     * @param depth   可视化深度
     * @return 可视化数据（包含节点和边）
     */
    Map<String, Object> generateTableLineageVisualization(Long tableId, Integer depth);

    /**
     * 生成列级血缘可视化数据
     *
     * @param columnId 列ID
     * @param depth    可视化深度
     * @return 可视化数据（包含节点和边）
     */
    Map<String, Object> generateColumnLineageVisualization(Long columnId, Integer depth);

    /**
     * 生成数据库级血缘全景图
     *
     * @param databaseId 数据库ID
     * @return 数据库血缘全景可视化数据
     */
    Map<String, Object> generateDatabaseLineageOverview(Long databaseId);

    /**
     * 生成血缘路径可视化数据
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 血缘路径可视化数据
     */
    Map<String, Object> generateLineagePathVisualization(Long sourceTableId, Long targetTableId);

    /**
     * 生成列级血缘路径可视化数据
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 列级血缘路径可视化数据
     */
    Map<String, Object> generateColumnLineagePathVisualization(Long sourceColumnId, Long targetColumnId);

    /**
     * 生成影响分析可视化数据
     *
     * @param tableId 表ID
     * @param depth   影响分析深度
     * @return 影响分析可视化数据
     */
    Map<String, Object> generateImpactAnalysisVisualization(Long tableId, Integer depth);

    /**
     * 生成数据流可视化数据
     *
     * @param tableId 表ID
     * @param depth   数据流深度
     * @return 数据流可视化数据
     */
    Map<String, Object> generateDataFlowVisualization(Long tableId, Integer depth);

    /**
     * 生成血缘热力图数据
     *
     * @param databaseId 数据库ID
     * @return 血缘热力图数据
     */
    Map<String, Object> generateLineageHeatmap(Long databaseId);

    /**
     * 生成血缘统计图表数据
     *
     * @param databaseId 数据库ID
     * @return 血缘统计图表数据
     */
    Map<String, Object> generateLineageStatisticsChart(Long databaseId);

    /**
     * 生成血缘质量评估可视化数据
     *
     * @param databaseId 数据库ID
     * @return 血缘质量评估可视化数据
     */
    Map<String, Object> generateLineageQualityVisualization(Long databaseId);

    /**
     * 生成血缘复杂度分析图
     *
     * @param tableId 表ID
     * @return 血缘复杂度分析图数据
     */
    Map<String, Object> generateLineageComplexityChart(Long tableId);

    /**
     * 生成血缘时间线可视化数据
     *
     * @param tableId 表ID
     * @return 血缘时间线可视化数据
     */
    Map<String, Object> generateLineageTimeline(Long tableId);

    /**
     * 生成自定义血缘视图
     *
     * @param config 自定义配置
     * @return 自定义血缘视图数据
     */
    Map<String, Object> generateCustomLineageView(Map<String, Object> config);

    /**
     * 导出血缘图为图片格式
     *
     * @param tableId 表ID
     * @param format  图片格式（PNG, SVG, PDF等）
     * @return 图片数据
     */
    byte[] exportLineageGraph(Long tableId, String format);

    /**
     * 生成血缘报告数据
     *
     * @param databaseId 数据库ID
     * @return 血缘报告数据
     */
    Map<String, Object> generateLineageReport(Long databaseId);

    /**
     * 获取可视化配置选项
     *
     * @return 可视化配置选项
     */
    Map<String, Object> getVisualizationOptions();

    /**
     * 验证可视化数据的完整性
     *
     * @param visualizationData 可视化数据
     * @return 验证结果
     */
    Map<String, Object> validateVisualizationData(Map<String, Object> visualizationData);
}
