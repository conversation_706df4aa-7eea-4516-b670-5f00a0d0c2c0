package com.data.platform.datamind.server.datameta.lineage;

import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;

import java.util.List;
import java.util.Map;

/**
 * 血缘分析器接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.lineage
 * @description 血缘分析器，提供数据血缘分析功能
 * @since 1.8
 */
public interface LineageAnalyzer {

    /**
     * 分析表的血缘关系
     *
     * @param tableId 表ID
     * @return 表血缘关系信息
     */
    TableLineageRespVO analyzeTableLineage(Long tableId);

    /**
     * 分析表的血缘关系（指定深度）
     *
     * @param tableId 表ID
     * @param depth   分析深度
     * @return 表血缘关系信息
     */
    TableLineageRespVO analyzeTableLineage(Long tableId, Integer depth);

    /**
     * 分析列的血缘关系
     *
     * @param columnId 列ID
     * @return 列血缘关系信息
     */
    List<LineageNodeVO> analyzeColumnLineage(Long columnId);

    /**
     * 分析列的血缘关系（指定深度）
     *
     * @param columnId 列ID
     * @param depth    分析深度
     * @return 列血缘关系信息
     */
    List<LineageNodeVO> analyzeColumnLineage(Long columnId, Integer depth);

    /**
     * 获取表的上游依赖
     *
     * @param tableId 表ID
     * @param depth   查询深度
     * @return 上游表列表
     */
    List<LineageNodeVO> getUpstreamTables(Long tableId, Integer depth);

    /**
     * 获取表的下游依赖
     *
     * @param tableId 表ID
     * @param depth   查询深度
     * @return 下游表列表
     */
    List<LineageNodeVO> getDownstreamTables(Long tableId, Integer depth);

    /**
     * 获取列的上游依赖
     *
     * @param columnId 列ID
     * @param depth    查询深度
     * @return 上游列列表
     */
    List<LineageNodeVO> getUpstreamColumns(Long columnId, Integer depth);

    /**
     * 获取列的下游依赖
     *
     * @param columnId 列ID
     * @param depth    查询深度
     * @return 下游列列表
     */
    List<LineageNodeVO> getDownstreamColumns(Long columnId, Integer depth);

    /**
     * 分析血缘路径
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 血缘路径
     */
    List<LineageNodeVO> analyzeLineagePath(Long sourceTableId, Long targetTableId);

    /**
     * 分析列级血缘路径
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 血缘路径
     */
    List<LineageNodeVO> analyzeColumnLineagePath(Long sourceColumnId, Long targetColumnId);

    /**
     * 获取血缘统计信息
     *
     * @param tableId 表ID
     * @return 血缘统计信息
     */
    Map<String, Object> getLineageStatistics(Long tableId);

    /**
     * 获取数据库级别的血缘统计
     *
     * @param databaseId 数据库ID
     * @return 数据库血缘统计信息
     */
    Map<String, Object> getDatabaseLineageStatistics(Long databaseId);

    /**
     * 验证血缘关系的完整性
     *
     * @param tableId 表ID
     * @return 验证结果
     */
    Map<String, Object> validateLineageIntegrity(Long tableId);

    /**
     * 检测血缘环路
     *
     * @param databaseId 数据库ID
     * @return 环路检测结果
     */
    List<List<LineageNodeVO>> detectLineageCycles(Long databaseId);

    /**
     * 获取血缘关系的复杂度分析
     *
     * @param tableId 表ID
     * @return 复杂度分析结果
     */
    Map<String, Object> analyzeLineageComplexity(Long tableId);

    /**
     * 获取血缘关系的质量评分
     *
     * @param tableId 表ID
     * @return 质量评分
     */
    Map<String, Object> getLineageQualityScore(Long tableId);
}
