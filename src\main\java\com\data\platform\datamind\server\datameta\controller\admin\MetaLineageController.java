package com.data.platform.datamind.server.datameta.controller.admin;

import com.data.platform.datamind.server.datameta.lineage.DataFlowTracker;
import com.data.platform.datamind.server.datameta.lineage.ImpactAnalyzer;
import com.data.platform.datamind.server.datameta.lineage.LineageAnalyzer;
import com.data.platform.datamind.server.datameta.lineage.LineageVisualizer;
import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowPathVO;
import com.data.platform.datamind.server.datameta.vo.lineage.ImpactAnalysisRespVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 数据血缘分析控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "管理后台 - 数据血缘分析")
@RestController
@RequestMapping("/admin-api/data-meta/lineage")
@Validated
@Slf4j
public class MetaLineageController {

    @Resource
    private LineageAnalyzer lineageAnalyzer;

    @Resource
    private DataFlowTracker dataFlowTracker;

    @Resource
    private LineageVisualizer lineageVisualizer;

    @Resource
    private ImpactAnalyzer impactAnalyzer;

    // ==================== 血缘分析接口 ====================

    @GetMapping("/table/{tableId}")
    @Operation(summary = "获取表的血缘关系")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "分析深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<TableLineageRespVO> getTableLineage(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        TableLineageRespVO lineage = lineageAnalyzer.analyzeTableLineage(tableId, depth);
        return success(lineage);
    }

    @GetMapping("/column/{columnId}")
    @Operation(summary = "获取列的血缘关系")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "分析深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getColumnLineage(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> lineage = lineageAnalyzer.analyzeColumnLineage(columnId, depth);
        return success(lineage);
    }

    @GetMapping("/upstream/table/{tableId}")
    @Operation(summary = "获取表的上游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getUpstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> upstream = lineageAnalyzer.getUpstreamTables(tableId, depth);
        return success(upstream);
    }

    @GetMapping("/downstream/table/{tableId}")
    @Operation(summary = "获取表的下游依赖")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getDownstreamTables(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> downstream = lineageAnalyzer.getDownstreamTables(tableId, depth);
        return success(downstream);
    }

    @GetMapping("/upstream/column/{columnId}")
    @Operation(summary = "获取列的上游依赖")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getUpstreamColumns(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> upstream = lineageAnalyzer.getUpstreamColumns(columnId, depth);
        return success(upstream);
    }

    @GetMapping("/downstream/column/{columnId}")
    @Operation(summary = "获取列的下游依赖")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "查询深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> getDownstreamColumns(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<LineageNodeVO> downstream = lineageAnalyzer.getDownstreamColumns(columnId, depth);
        return success(downstream);
    }

    @GetMapping("/path/table")
    @Operation(summary = "分析表级血缘路径")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> analyzeTableLineagePath(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        List<LineageNodeVO> path = lineageAnalyzer.analyzeLineagePath(sourceTableId, targetTableId);
        return success(path);
    }

    @GetMapping("/path/column")
    @Operation(summary = "分析列级血缘路径")
    @Parameter(name = "sourceColumnId", description = "源列ID", required = true)
    @Parameter(name = "targetColumnId", description = "目标列ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<LineageNodeVO>> analyzeColumnLineagePath(
            @RequestParam("sourceColumnId") @NotNull @Positive Long sourceColumnId,
            @RequestParam("targetColumnId") @NotNull @Positive Long targetColumnId) {
        List<LineageNodeVO> path = lineageAnalyzer.analyzeColumnLineagePath(sourceColumnId, targetColumnId);
        return success(path);
    }

    @GetMapping("/statistics/table/{tableId}")
    @Operation(summary = "获取表的血缘统计信息")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getTableLineageStatistics(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> statistics = lineageAnalyzer.getLineageStatistics(tableId);
        return success(statistics);
    }

    @GetMapping("/statistics/database/{databaseId}")
    @Operation(summary = "获取数据库的血缘统计信息")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getDatabaseLineageStatistics(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> statistics = lineageAnalyzer.getDatabaseLineageStatistics(databaseId);
        return success(statistics);
    }

    // ==================== 数据流追踪接口 ====================

    @GetMapping("/dataflow/table")
    @Operation(summary = "追踪表级数据流")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<DataFlowPathVO> trackTableDataFlow(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        DataFlowPathVO dataFlow = dataFlowTracker.trackTableDataFlow(sourceTableId, targetTableId);
        return success(dataFlow);
    }

    @GetMapping("/dataflow/column")
    @Operation(summary = "追踪列级数据流")
    @Parameter(name = "sourceColumnId", description = "源列ID", required = true)
    @Parameter(name = "targetColumnId", description = "目标列ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<DataFlowPathVO> trackColumnDataFlow(
            @RequestParam("sourceColumnId") @NotNull @Positive Long sourceColumnId,
            @RequestParam("targetColumnId") @NotNull @Positive Long targetColumnId) {
        DataFlowPathVO dataFlow = dataFlowTracker.trackColumnDataFlow(sourceColumnId, targetColumnId);
        return success(dataFlow);
    }

    @GetMapping("/dataflow/inflow/table/{tableId}")
    @Operation(summary = "获取表的数据流入路径")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "追踪深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<DataFlowPathVO>> getDataInflowPaths(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<DataFlowPathVO> inflowPaths = dataFlowTracker.getDataInflowPaths(tableId, depth);
        return success(inflowPaths);
    }

    @GetMapping("/dataflow/outflow/table/{tableId}")
    @Operation(summary = "获取表的数据流出路径")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "追踪深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<DataFlowPathVO>> getDataOutflowPaths(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        List<DataFlowPathVO> outflowPaths = dataFlowTracker.getDataOutflowPaths(tableId, depth);
        return success(outflowPaths);
    }

    @GetMapping("/dataflow/complexity/table/{tableId}")
    @Operation(summary = "分析表的数据流复杂度")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> analyzeDataFlowComplexity(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> complexity = dataFlowTracker.analyzeDataFlowComplexity(tableId);
        return success(complexity);
    }

    @GetMapping("/dataflow/statistics/table/{tableId}")
    @Operation(summary = "获取表的数据流统计信息")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getDataFlowStatistics(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> statistics = dataFlowTracker.getDataFlowStatistics(tableId);
        return success(statistics);
    }

    // ==================== 可视化接口 ====================

    @GetMapping("/visualization/table/{tableId}")
    @Operation(summary = "生成表血缘可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "可视化深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateTableLineageVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateTableLineageVisualization(tableId, depth);
        return success(visualization);
    }

    @GetMapping("/visualization/column/{columnId}")
    @Operation(summary = "生成列血缘可视化数据")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "depth", description = "可视化深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateColumnLineageVisualization(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateColumnLineageVisualization(columnId, depth);
        return success(visualization);
    }

    @GetMapping("/visualization/database/{databaseId}")
    @Operation(summary = "生成数据库血缘全景图")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateDatabaseLineageOverview(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        Map<String, Object> overview = lineageVisualizer.generateDatabaseLineageOverview(databaseId);
        return success(overview);
    }

    @GetMapping("/visualization/path/table")
    @Operation(summary = "生成表血缘路径可视化数据")
    @Parameter(name = "sourceTableId", description = "源表ID", required = true)
    @Parameter(name = "targetTableId", description = "目标表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateLineagePathVisualization(
            @RequestParam("sourceTableId") @NotNull @Positive Long sourceTableId,
            @RequestParam("targetTableId") @NotNull @Positive Long targetTableId) {
        Map<String, Object> visualization = lineageVisualizer.generateLineagePathVisualization(sourceTableId, targetTableId);
        return success(visualization);
    }

    @GetMapping("/visualization/dataflow/{tableId}")
    @Operation(summary = "生成数据流可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "数据流深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateDataFlowVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "2") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateDataFlowVisualization(tableId, depth);
        return success(visualization);
    }

    // ==================== 影响分析接口 ====================

    @PostMapping("/impact/table/{tableId}")
    @Operation(summary = "分析表变更的影响")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "changeType", description = "变更类型", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:impact')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<ImpactAnalysisRespVO> analyzeTableImpact(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam("changeType") @NotNull String changeType) {
        ImpactAnalysisRespVO impact = impactAnalyzer.analyzeTableImpact(tableId, changeType);
        return success(impact);
    }

    @PostMapping("/impact/column/{columnId}")
    @Operation(summary = "分析列变更的影响")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @Parameter(name = "changeType", description = "变更类型", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:impact')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<ImpactAnalysisRespVO> analyzeColumnImpact(
            @PathVariable("columnId") @NotNull @Positive Long columnId,
            @RequestParam("changeType") @NotNull String changeType) {
        ImpactAnalysisRespVO impact = impactAnalyzer.analyzeColumnImpact(columnId, changeType);
        return success(impact);
    }

    @GetMapping("/impact/score/table/{tableId}")
    @Operation(summary = "计算表的影响范围评分")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Integer> calculateTableImpactScore(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Integer score = impactAnalyzer.calculateImpactScore(tableId);
        return success(score);
    }

    @GetMapping("/impact/score/column/{columnId}")
    @Operation(summary = "计算列的影响范围评分")
    @Parameter(name = "columnId", description = "列ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Integer> calculateColumnImpactScore(
            @PathVariable("columnId") @NotNull @Positive Long columnId) {
        Integer score = impactAnalyzer.calculateColumnImpactScore(columnId);
        return success(score);
    }

    @GetMapping("/impact/statistics/table/{tableId}")
    @Operation(summary = "获取表的影响分析统计信息")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getTableImpactStatistics(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> statistics = impactAnalyzer.getImpactStatistics(tableId);
        return success(statistics);
    }

    @GetMapping("/impact/visualization/{tableId}")
    @Operation(summary = "生成影响分析可视化数据")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @Parameter(name = "depth", description = "影响分析深度", required = false)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> generateImpactAnalysisVisualization(
            @PathVariable("tableId") @NotNull @Positive Long tableId,
            @RequestParam(value = "depth", defaultValue = "3") Integer depth) {
        Map<String, Object> visualization = lineageVisualizer.generateImpactAnalysisVisualization(tableId, depth);
        return success(visualization);
    }

    // ==================== 质量和验证接口 ====================

    @GetMapping("/quality/table/{tableId}")
    @Operation(summary = "获取表血缘质量评分")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> getTableLineageQuality(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> quality = lineageAnalyzer.getLineageQualityScore(tableId);
        return success(quality);
    }

    @GetMapping("/complexity/table/{tableId}")
    @Operation(summary = "分析表血缘复杂度")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> analyzeTableLineageComplexity(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> complexity = lineageAnalyzer.analyzeLineageComplexity(tableId);
        return success(complexity);
    }

    @GetMapping("/validate/table/{tableId}")
    @Operation(summary = "验证表血缘关系完整性")
    @Parameter(name = "tableId", description = "表ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<Map<String, Object>> validateTableLineageIntegrity(
            @PathVariable("tableId") @NotNull @Positive Long tableId) {
        Map<String, Object> validation = lineageAnalyzer.validateLineageIntegrity(tableId);
        return success(validation);
    }

    @GetMapping("/cycles/database/{databaseId}")
    @Operation(summary = "检测数据库血缘环路")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    @PreAuthorize("@ss.hasPermission('meta:lineage:query')")
    public cn.iocoder.yudao.framework.common.pojo.CommonResult<List<List<LineageNodeVO>>> detectLineageCycles(
            @PathVariable("databaseId") @NotNull @Positive Long databaseId) {
        List<List<LineageNodeVO>> cycles = lineageAnalyzer.detectLineageCycles(databaseId);
        return success(cycles);
    }
}
