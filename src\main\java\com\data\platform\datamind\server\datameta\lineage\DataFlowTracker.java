package com.data.platform.datamind.server.datameta.lineage;

import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowPathVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;

import java.util.List;
import java.util.Map;

/**
 * 数据流追踪器接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.lineage
 * @description 数据流追踪器，提供数据流向追踪功能
 * @since 1.8
 */
public interface DataFlowTracker {

    /**
     * 追踪表级数据流
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 数据流路径
     */
    DataFlowPathVO trackTableDataFlow(Long sourceTableId, Long targetTableId);

    /**
     * 追踪列级数据流
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 数据流路径
     */
    DataFlowPathVO trackColumnDataFlow(Long sourceColumnId, Long targetColumnId);

    /**
     * 获取表的所有数据流入路径
     *
     * @param tableId 表ID
     * @param depth   追踪深度
     * @return 数据流入路径列表
     */
    List<DataFlowPathVO> getDataInflowPaths(Long tableId, Integer depth);

    /**
     * 获取表的所有数据流出路径
     *
     * @param tableId 表ID
     * @param depth   追踪深度
     * @return 数据流出路径列表
     */
    List<DataFlowPathVO> getDataOutflowPaths(Long tableId, Integer depth);

    /**
     * 获取列的所有数据流入路径
     *
     * @param columnId 列ID
     * @param depth    追踪深度
     * @return 数据流入路径列表
     */
    List<DataFlowPathVO> getColumnInflowPaths(Long columnId, Integer depth);

    /**
     * 获取列的所有数据流出路径
     *
     * @param columnId 列ID
     * @param depth    追踪深度
     * @return 数据流出路径列表
     */
    List<DataFlowPathVO> getColumnOutflowPaths(Long columnId, Integer depth);

    /**
     * 分析数据流的复杂度
     *
     * @param tableId 表ID
     * @return 数据流复杂度分析
     */
    Map<String, Object> analyzeDataFlowComplexity(Long tableId);

    /**
     * 获取数据流的统计信息
     *
     * @param tableId 表ID
     * @return 数据流统计信息
     */
    Map<String, Object> getDataFlowStatistics(Long tableId);

    /**
     * 检测数据流的瓶颈
     *
     * @param databaseId 数据库ID
     * @return 数据流瓶颈分析
     */
    List<Map<String, Object>> detectDataFlowBottlenecks(Long databaseId);

    /**
     * 分析数据流的质量
     *
     * @param tableId 表ID
     * @return 数据流质量分析
     */
    Map<String, Object> analyzeDataFlowQuality(Long tableId);

    /**
     * 获取数据流的热点表
     *
     * @param databaseId 数据库ID
     * @param limit      返回数量限制
     * @return 热点表列表
     */
    List<Map<String, Object>> getDataFlowHotspots(Long databaseId, Integer limit);

    /**
     * 追踪数据的完整生命周期
     *
     * @param tableId 表ID
     * @return 数据生命周期信息
     */
    Map<String, Object> trackDataLifecycle(Long tableId);

    /**
     * 分析数据流的时间特征
     *
     * @param tableId 表ID
     * @return 数据流时间特征分析
     */
    Map<String, Object> analyzeDataFlowTiming(Long tableId);

    /**
     * 获取数据流的依赖强度
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 依赖强度分析
     */
    Map<String, Object> getDataFlowDependencyStrength(Long sourceTableId, Long targetTableId);

    /**
     * 预测数据流的影响范围
     *
     * @param tableId 表ID
     * @return 影响范围预测
     */
    Map<String, Object> predictDataFlowImpact(Long tableId);

    /**
     * 优化数据流路径建议
     *
     * @param tableId 表ID
     * @return 优化建议
     */
    List<String> suggestDataFlowOptimization(Long tableId);
}
