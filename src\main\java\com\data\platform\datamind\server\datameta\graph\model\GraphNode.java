package com.data.platform.datamind.server.datameta.graph.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 图谱节点模型
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GraphNode {
    
    /**
     * 节点标签（如Database、Table、Column）
     */
    private String label;
    
    /**
     * 节点属性
     */
    private Map<String, Object> properties;
    
    /**
     * 获取节点ID
     *
     * @return 节点ID
     */
    public Object getId() {
        return properties != null ? properties.get("id") : null;
    }
    
    /**
     * 获取节点名称
     *
     * @return 节点名称
     */
    public String getName() {
        if (properties == null) {
            return null;
        }
        
        // 根据不同标签返回相应的名称字段
        switch (label) {
            case "Database":
                return (String) properties.get("name");
            case "Table":
                return (String) properties.get("tableName");
            case "Column":
                return (String) properties.get("columnName");
            default:
                return (String) properties.get("name");
        }
    }
    
    /**
     * 获取RAG文本
     *
     * @return RAG友好的文本描述
     */
    public String getRagText() {
        return properties != null ? (String) properties.get("ragText") : null;
    }
}
