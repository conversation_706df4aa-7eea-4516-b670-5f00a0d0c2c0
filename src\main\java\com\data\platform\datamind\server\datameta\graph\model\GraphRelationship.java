package com.data.platform.datamind.server.datameta.graph.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 图谱关系模型
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GraphRelationship {
    
    /**
     * 关系类型（如CONTAINS_TABLE、HAS_COLUMN、REFERENCES）
     */
    private String type;
    
    /**
     * 源节点ID
     */
    private Object sourceNodeId;
    
    /**
     * 目标节点ID
     */
    private Object targetNodeId;
    
    /**
     * 关系属性
     */
    private Map<String, Object> properties;
    
    /**
     * 获取关系描述
     *
     * @return 关系描述
     */
    public String getDescription() {
        switch (type) {
            case "CONTAINS_TABLE":
                return "包含表";
            case "HAS_COLUMN":
                return "包含列";
            case "REFERENCES":
                return "引用";
            case "RELATED_TO":
                return "关联";
            default:
                return type;
        }
    }
}
