package com.data.platform.datamind.server.datameta.graph;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.graph.model.GraphNode;
import com.data.platform.datamind.server.datameta.graph.model.GraphRelationship;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 图谱模型构建器
 * 负责将关系型元数据转换为图谱模型
 *
 * <AUTHOR> Team
 */
@Component
@Slf4j
public class GraphModelBuilder {

    /**
     * 构建数据库节点
     *
     * @param database 数据库DO对象
     * @return 图谱节点
     */
    public GraphNode buildDatabaseNode(MetaDatabaseDO database) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", database.getId());
        properties.put("name", database.getName());
        properties.put("type", database.getType());
        properties.put("instanceName", database.getInstanceName());
        properties.put("description", database.getDescription());
        properties.put("host", database.getHost());
        properties.put("port", database.getPort());
        properties.put("status", database.getStatus());
        properties.put("regionFlag", database.getRegionFlag());
        
        // 构建RAG友好的文本描述
        StringBuilder ragText = new StringBuilder();
        ragText.append("数据库: ").append(database.getName())
               .append(", 类型: ").append(database.getType())
               .append(", 实例: ").append(database.getInstanceName());
        if (database.getDescription() != null) {
            ragText.append(", 描述: ").append(database.getDescription());
        }
        properties.put("ragText", ragText.toString());
        
        return GraphNode.builder()
                .label("Database")
                .properties(properties)
                .build();
    }

    /**
     * 构建表节点
     *
     * @param table 表DO对象
     * @return 图谱节点
     */
    public GraphNode buildTableNode(MetaTableDO table) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", table.getId());
        properties.put("dbId", table.getDbId());
        properties.put("tableName", table.getTableName());
        properties.put("tableComment", table.getTableComment());
        properties.put("tableOwner", table.getTableOwner());
        properties.put("tableRows", table.getTableRows());
        properties.put("dataLength", table.getDataLength());
        properties.put("engine", table.getEngine());
        properties.put("charset", table.getCharset());
        properties.put("partitionNum", table.getPartitionNum());
        
        // 构建RAG友好的文本描述
        StringBuilder ragText = new StringBuilder();
        ragText.append("表: ").append(table.getTableName());
        if (table.getTableComment() != null) {
            ragText.append(", 注释: ").append(table.getTableComment());
        }
        if (table.getTableRows() != null) {
            ragText.append(", 行数: ").append(table.getTableRows());
        }
        if (table.getEngine() != null) {
            ragText.append(", 引擎: ").append(table.getEngine());
        }
        properties.put("ragText", ragText.toString());
        
        return GraphNode.builder()
                .label("Table")
                .properties(properties)
                .build();
    }

    /**
     * 构建列节点
     *
     * @param column 列DO对象
     * @return 图谱节点
     */
    public GraphNode buildColumnNode(MetaColumnDO column) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", column.getId());
        properties.put("tableId", column.getTableId());
        properties.put("columnName", column.getColumnName());
        properties.put("columnType", column.getColumnType());
        properties.put("dataType", column.getDataType());
        properties.put("columnComment", column.getColumnComment());
        properties.put("columnKey", column.getColumnKey());
        properties.put("isNullable", column.getIsNullable());
        properties.put("columnDefault", column.getColumnDefault());
        properties.put("extra", column.getExtra());
        properties.put("isPrimaryKey", column.getIsPrimaryKey());
        properties.put("isForeignKey", column.getIsForeignKey());
        properties.put("fkTableId", column.getFkTableId());
        properties.put("fkColumnId", column.getFkColumnId());
        
        // 构建RAG友好的文本描述
        StringBuilder ragText = new StringBuilder();
        ragText.append("列: ").append(column.getColumnName())
               .append(", 类型: ").append(column.getDataType());
        if (column.getColumnComment() != null) {
            ragText.append(", 注释: ").append(column.getColumnComment());
        }
        if (Boolean.TRUE.equals(column.getIsPrimaryKey())) {
            ragText.append(", 主键");
        }
        if (Boolean.TRUE.equals(column.getIsForeignKey())) {
            ragText.append(", 外键");
        }
        properties.put("ragText", ragText.toString());
        
        return GraphNode.builder()
                .label("Column")
                .properties(properties)
                .build();
    }

    /**
     * 构建数据库-表关系
     *
     * @param databaseId 数据库ID
     * @param tableId 表ID
     * @return 图谱关系
     */
    public GraphRelationship buildDatabaseTableRelationship(Long databaseId, Long tableId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("createTime", System.currentTimeMillis());
        
        return GraphRelationship.builder()
                .type("CONTAINS_TABLE")
                .sourceNodeId(databaseId)
                .targetNodeId(tableId)
                .properties(properties)
                .build();
    }

    /**
     * 构建表-列关系
     *
     * @param tableId 表ID
     * @param columnId 列ID
     * @return 图谱关系
     */
    public GraphRelationship buildTableColumnRelationship(Long tableId, Long columnId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("createTime", System.currentTimeMillis());
        
        return GraphRelationship.builder()
                .type("HAS_COLUMN")
                .sourceNodeId(tableId)
                .targetNodeId(columnId)
                .properties(properties)
                .build();
    }

    /**
     * 构建外键关系
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 图谱关系
     */
    public GraphRelationship buildForeignKeyRelationship(Long sourceColumnId, Long targetColumnId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("relationshipType", "FOREIGN_KEY");
        properties.put("createTime", System.currentTimeMillis());
        
        return GraphRelationship.builder()
                .type("REFERENCES")
                .sourceNodeId(sourceColumnId)
                .targetNodeId(targetColumnId)
                .properties(properties)
                .build();
    }

    /**
     * 构建表间关系（基于外键）
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @param relationshipType 关系类型
     * @return 图谱关系
     */
    public GraphRelationship buildTableRelationship(Long sourceTableId, Long targetTableId, String relationshipType) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("relationshipType", relationshipType);
        properties.put("createTime", System.currentTimeMillis());
        
        return GraphRelationship.builder()
                .type("RELATED_TO")
                .sourceNodeId(sourceTableId)
                .targetNodeId(targetTableId)
                .properties(properties)
                .build();
    }

    /**
     * 批量构建数据库节点
     *
     * @param databases 数据库列表
     * @return 图谱节点列表
     */
    public List<GraphNode> buildDatabaseNodes(List<MetaDatabaseDO> databases) {
        List<GraphNode> nodes = new ArrayList<>();
        for (MetaDatabaseDO database : databases) {
            nodes.add(buildDatabaseNode(database));
        }
        log.info("Built {} database nodes", nodes.size());
        return nodes;
    }

    /**
     * 批量构建表节点
     *
     * @param tables 表列表
     * @return 图谱节点列表
     */
    public List<GraphNode> buildTableNodes(List<MetaTableDO> tables) {
        List<GraphNode> nodes = new ArrayList<>();
        for (MetaTableDO table : tables) {
            nodes.add(buildTableNode(table));
        }
        log.info("Built {} table nodes", nodes.size());
        return nodes;
    }

    /**
     * 批量构建列节点
     *
     * @param columns 列列表
     * @return 图谱节点列表
     */
    public List<GraphNode> buildColumnNodes(List<MetaColumnDO> columns) {
        List<GraphNode> nodes = new ArrayList<>();
        for (MetaColumnDO column : columns) {
            nodes.add(buildColumnNode(column));
        }
        log.info("Built {} column nodes", nodes.size());
        return nodes;
    }

    /**
     * 构建所有关系
     *
     * @param databases 数据库列表
     * @param tables 表列表
     * @param columns 列列表
     * @return 图谱关系列表
     */
    public List<GraphRelationship> buildAllRelationships(List<MetaDatabaseDO> databases, 
                                                         List<MetaTableDO> tables, 
                                                         List<MetaColumnDO> columns) {
        List<GraphRelationship> relationships = new ArrayList<>();
        
        // 构建数据库-表关系
        for (MetaTableDO table : tables) {
            relationships.add(buildDatabaseTableRelationship(table.getDbId(), table.getId()));
        }
        
        // 构建表-列关系
        for (MetaColumnDO column : columns) {
            relationships.add(buildTableColumnRelationship(column.getTableId(), column.getId()));
        }
        
        // 构建外键关系
        for (MetaColumnDO column : columns) {
            if (Boolean.TRUE.equals(column.getIsForeignKey()) && column.getFkColumnId() != null) {
                relationships.add(buildForeignKeyRelationship(column.getId(), column.getFkColumnId()));
            }
        }
        
        log.info("Built {} relationships", relationships.size());
        return relationships;
    }
}
