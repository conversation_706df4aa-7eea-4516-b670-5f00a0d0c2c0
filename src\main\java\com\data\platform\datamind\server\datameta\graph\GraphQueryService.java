package com.data.platform.datamind.server.datameta.graph;

import com.data.platform.datamind.server.datameta.graph.model.GraphNode;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageRelationVO;
import com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 图谱查询服务
 * 提供各种图谱查询功能，包括血缘分析、关系查询等
 *
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class GraphQueryService {

    @Resource
    private Neo4jService neo4jService;

    /**
     * 获取表的关系图谱
     *
     * @param tableId 表ID
     * @return 表关系图谱数据
     */
    public Map<String, Object> getTableGraph(Long tableId) {
        log.info("Getting table graph for tableId: {}", tableId);

        String cypher = """
            MATCH (t:Table {id: $tableId})-[:HAS_COLUMN]->(c:Column)
            OPTIONAL MATCH (c)-[r:REFERENCES]->(target_c:Column)<-[:HAS_COLUMN]-(target_t:Table)
            RETURN t, c, r, target_c, target_t
            """;

        Map<String, Object> params = Map.of("tableId", tableId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildTableGraphResponse(results);
    }

    /**
     * 获取列的血缘关系
     *
     * @param columnId 列ID
     * @return 列血缘关系
     */
    public List<LineageNodeVO> getColumnLineage(Long columnId) {
        log.info("Getting column lineage for columnId: {}", columnId);

        String cypher = """
            MATCH path = (c:Column {id: $columnId})-[:REFERENCES*1..5]-(related:Column)
            RETURN path
            """;

        Map<String, Object> params = Map.of("columnId", columnId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildLineageResponse(results);
    }

    /**
     * 获取表的上游依赖
     *
     * @param tableId 表ID
     * @param depth 查询深度
     * @return 上游依赖表列表
     */
    public List<TableRelationshipVO> getUpstreamTables(Long tableId, int depth) {
        log.info("Getting upstream tables for tableId: {}, depth: {}", tableId, depth);

        String cypher = String.format("""
            MATCH (t:Table {id: $tableId})-[:HAS_COLUMN]->(c:Column)-[:REFERENCES*1..%d]-(upstream_c:Column)<-[:HAS_COLUMN]-(upstream_t:Table)
            RETURN DISTINCT upstream_t, count(c) as relationCount
            ORDER BY relationCount DESC
            """, depth);

        Map<String, Object> params = Map.of("tableId", tableId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildTableRelationshipResponse(results, "UPSTREAM");
    }

    /**
     * 获取表的下游依赖
     *
     * @param tableId 表ID
     * @param depth 查询深度
     * @return 下游依赖表列表
     */
    public List<TableRelationshipVO> getDownstreamTables(Long tableId, int depth) {
        log.info("Getting downstream tables for tableId: {}, depth: {}", tableId, depth);

        String cypher = String.format("""
            MATCH (t:Table {id: $tableId})-[:HAS_COLUMN]->(c:Column)<-[:REFERENCES*1..%d]-(downstream_c:Column)<-[:HAS_COLUMN]-(downstream_t:Table)
            RETURN DISTINCT downstream_t, count(c) as relationCount
            ORDER BY relationCount DESC
            """, depth);

        Map<String, Object> params = Map.of("tableId", tableId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildTableRelationshipResponse(results, "DOWNSTREAM");
    }

    /**
     * 获取数据库的表关系概览
     *
     * @param databaseId 数据库ID
     * @return 表关系概览
     */
    public Map<String, Object> getDatabaseTableOverview(Long databaseId) {
        log.info("Getting database table overview for databaseId: {}", databaseId);

        String cypher = """
            MATCH (db:Database {id: $databaseId})-[:CONTAINS_TABLE]->(t:Table)
            OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
            OPTIONAL MATCH (c)-[r:REFERENCES]->()
            RETURN t.id as tableId, t.tableName as tableName, t.tableComment as tableComment,
                   count(DISTINCT c) as columnCount, count(DISTINCT r) as relationshipCount
            ORDER BY relationshipCount DESC, columnCount DESC
            """;

        Map<String, Object> params = Map.of("databaseId", databaseId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildDatabaseOverviewResponse(results);
    }

    /**
     * 搜索相关表
     *
     * @param keyword 搜索关键词
     * @param limit 结果限制
     * @return 搜索结果
     */
    public List<Map<String, Object>> searchTables(String keyword, int limit) {
        log.info("Searching tables with keyword: {}, limit: {}", keyword, limit);

        String cypher = """
            MATCH (t:Table)
            WHERE t.tableName CONTAINS $keyword OR t.tableComment CONTAINS $keyword OR t.ragText CONTAINS $keyword
            OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
            RETURN t.id as tableId, t.tableName as tableName, t.tableComment as tableComment,
                   t.ragText as ragText, count(c) as columnCount
            ORDER BY columnCount DESC
            LIMIT $limit
            """;

        Map<String, Object> params = Map.of("keyword", keyword, "limit", limit);
        return neo4jService.executeQuery(cypher, params);
    }

    /**
     * 获取表的影响分析
     *
     * @param tableId 表ID
     * @return 影响分析结果
     */
    public Map<String, Object> getTableImpactAnalysis(Long tableId) {
        log.info("Getting table impact analysis for tableId: {}", tableId);

        // 获取直接影响的表
        String directImpactCypher = """
            MATCH (t:Table {id: $tableId})-[:HAS_COLUMN]->(c:Column)<-[:REFERENCES]-(ref_c:Column)<-[:HAS_COLUMN]-(ref_t:Table)
            RETURN DISTINCT ref_t.id as tableId, ref_t.tableName as tableName, ref_t.tableComment as tableComment,
                   count(c) as impactedColumns
            ORDER BY impactedColumns DESC
            """;

        // 获取间接影响的表（2层深度）
        String indirectImpactCypher = """
            MATCH (t:Table {id: $tableId})-[:HAS_COLUMN]->(c:Column)<-[:REFERENCES*2]-(ref_c:Column)<-[:HAS_COLUMN]-(ref_t:Table)
            WHERE ref_t.id <> $tableId
            RETURN DISTINCT ref_t.id as tableId, ref_t.tableName as tableName, ref_t.tableComment as tableComment,
                   'INDIRECT' as impactType
            LIMIT 20
            """;

        Map<String, Object> params = Map.of("tableId", tableId);
        List<Map<String, Object>> directImpacts = neo4jService.executeQuery(directImpactCypher, params);
        List<Map<String, Object>> indirectImpacts = neo4jService.executeQuery(indirectImpactCypher, params);

        Map<String, Object> result = new HashMap<>();
        result.put("directImpacts", directImpacts);
        result.put("indirectImpacts", indirectImpacts);
        result.put("totalDirectCount", directImpacts.size());
        result.put("totalIndirectCount", indirectImpacts.size());

        return result;
    }

    /**
     * 构建表关系图谱响应
     */
    private Map<String, Object> buildTableGraphResponse(List<Map<String, Object>> results) {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> nodes = new ArrayList<>();
        List<Map<String, Object>> edges = new ArrayList<>();

        Set<Long> addedNodes = new HashSet<>();

        for (Map<String, Object> result : results) {
            // 处理表节点
            Map<String, Object> table = (Map<String, Object>) result.get("t");
            if (table != null) {
                Long tableId = (Long) table.get("id");
                if (!addedNodes.contains(tableId)) {
                    nodes.add(buildNodeResponse(table, "Table"));
                    addedNodes.add(tableId);
                }
            }

            // 处理列节点
            Map<String, Object> column = (Map<String, Object>) result.get("c");
            if (column != null) {
                Long columnId = (Long) column.get("id");
                if (!addedNodes.contains(columnId)) {
                    nodes.add(buildNodeResponse(column, "Column"));
                    addedNodes.add(columnId);
                }
            }

            // 处理关系
            Map<String, Object> relationship = (Map<String, Object>) result.get("r");
            if (relationship != null) {
                edges.add(buildEdgeResponse(relationship));
            }
        }

        response.put("nodes", nodes);
        response.put("edges", edges);
        return response;
    }

    /**
     * 构建血缘关系响应
     */
    private List<LineageNodeVO> buildLineageResponse(List<Map<String, Object>> results) {
        List<LineageNodeVO> lineageNodes = new ArrayList<>();
        // 实现血缘关系构建逻辑
        // 这里需要根据具体的VO结构来实现
        return lineageNodes;
    }

    /**
     * 构建表关系响应
     */
    private List<TableRelationshipVO> buildTableRelationshipResponse(List<Map<String, Object>> results, String relationType) {
        List<TableRelationshipVO> relationships = new ArrayList<>();
        // 实现表关系构建逻辑
        // 这里需要根据具体的VO结构来实现
        return relationships;
    }

    /**
     * 构建数据库概览响应
     */
    private Map<String, Object> buildDatabaseOverviewResponse(List<Map<String, Object>> results) {
        Map<String, Object> overview = new HashMap<>();
        overview.put("tables", results);
        overview.put("totalTables", results.size());
        
        int totalColumns = results.stream()
                .mapToInt(r -> ((Number) r.get("columnCount")).intValue())
                .sum();
        overview.put("totalColumns", totalColumns);
        
        int totalRelationships = results.stream()
                .mapToInt(r -> ((Number) r.get("relationshipCount")).intValue())
                .sum();
        overview.put("totalRelationships", totalRelationships);
        
        return overview;
    }

    /**
     * 构建节点响应
     */
    private Map<String, Object> buildNodeResponse(Map<String, Object> nodeData, String nodeType) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", nodeData.get("id"));
        node.put("type", nodeType);
        node.put("properties", nodeData);
        return node;
    }

    /**
     * 构建边响应
     */
    private Map<String, Object> buildEdgeResponse(Map<String, Object> relationshipData) {
        Map<String, Object> edge = new HashMap<>();
        edge.put("type", relationshipData.get("type"));
        edge.put("properties", relationshipData);
        return edge;
    }

    /**
     * 获取上游列
     *
     * @param columnId 列ID
     * @param depth    查询深度
     * @return 上游列列表
     */
    public List<LineageNodeVO> getUpstreamColumns(Long columnId, int depth) {
        log.info("Getting upstream columns for columnId: {}, depth: {}", columnId, depth);

        String cypher = String.format("""
            MATCH (c:Column {id: $columnId})-[:REFERENCES*1..%d]->(upstream_c:Column)<-[:HAS_COLUMN]-(upstream_t:Table)
            RETURN DISTINCT upstream_c, upstream_t
            ORDER BY upstream_t.tableName, upstream_c.columnName
            """, depth);

        Map<String, Object> params = Map.of("columnId", columnId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildColumnLineageResponse(results, "UPSTREAM");
    }

    /**
     * 获取下游列
     *
     * @param columnId 列ID
     * @param depth    查询深度
     * @return 下游列列表
     */
    public List<LineageNodeVO> getDownstreamColumns(Long columnId, int depth) {
        log.info("Getting downstream columns for columnId: {}, depth: {}", columnId, depth);

        String cypher = String.format("""
            MATCH (c:Column {id: $columnId})<-[:REFERENCES*1..%d]-(downstream_c:Column)<-[:HAS_COLUMN]-(downstream_t:Table)
            RETURN DISTINCT downstream_c, downstream_t
            ORDER BY downstream_t.tableName, downstream_c.columnName
            """, depth);

        Map<String, Object> params = Map.of("columnId", columnId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildColumnLineageResponse(results, "DOWNSTREAM");
    }

    /**
     * 获取血缘路径
     *
     * @param sourceTableId 源表ID
     * @param targetTableId 目标表ID
     * @return 血缘路径
     */
    public List<LineageNodeVO> getLineagePath(Long sourceTableId, Long targetTableId) {
        log.info("Getting lineage path from table {} to table {}", sourceTableId, targetTableId);

        String cypher = """
            MATCH path = shortestPath((source:Table {id: $sourceTableId})-[:HAS_COLUMN|REFERENCES*]-(target:Table {id: $targetTableId}))
            RETURN path
            """;

        Map<String, Object> params = Map.of("sourceTableId", sourceTableId, "targetTableId", targetTableId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildPathLineageResponse(results);
    }

    /**
     * 获取列级血缘路径
     *
     * @param sourceColumnId 源列ID
     * @param targetColumnId 目标列ID
     * @return 列级血缘路径
     */
    public List<LineageNodeVO> getColumnLineagePath(Long sourceColumnId, Long targetColumnId) {
        log.info("Getting column lineage path from column {} to column {}", sourceColumnId, targetColumnId);

        String cypher = """
            MATCH path = shortestPath((source:Column {id: $sourceColumnId})-[:REFERENCES*]-(target:Column {id: $targetColumnId}))
            RETURN path
            """;

        Map<String, Object> params = Map.of("sourceColumnId", sourceColumnId, "targetColumnId", targetColumnId);
        List<Map<String, Object>> results = neo4jService.executeQuery(cypher, params);

        return buildPathLineageResponse(results);
    }

    /**
     * 构建列血缘响应
     */
    private List<LineageNodeVO> buildColumnLineageResponse(List<Map<String, Object>> results, String direction) {
        List<LineageNodeVO> lineageNodes = new ArrayList<>();

        for (Map<String, Object> result : results) {
            Map<String, Object> column = (Map<String, Object>) result.get("upstream_c");
            if (column == null) {
                column = (Map<String, Object>) result.get("downstream_c");
            }

            Map<String, Object> table = (Map<String, Object>) result.get("upstream_t");
            if (table == null) {
                table = (Map<String, Object>) result.get("downstream_t");
            }

            if (column != null && table != null) {
                LineageNodeVO node = LineageNodeVO.builder()
                        .id((Long) column.get("id"))
                        .nodeType("COLUMN")
                        .nodeName((String) column.get("columnName"))
                        .nodeDescription((String) column.get("columnComment"))
                        .tableId((Long) table.get("id"))
                        .tableName((String) table.get("tableName"))
                        .databaseId((Long) table.get("dbId"))
                        .lineageDirection(direction)
                        .build();
                lineageNodes.add(node);
            }
        }

        return lineageNodes;
    }

    /**
     * 构建路径血缘响应
     */
    private List<LineageNodeVO> buildPathLineageResponse(List<Map<String, Object>> results) {
        List<LineageNodeVO> lineageNodes = new ArrayList<>();
        // 这里需要解析Neo4j路径结果，构建血缘节点列表
        // 具体实现取决于Neo4j驱动返回的路径数据结构
        return lineageNodes;
    }
}
