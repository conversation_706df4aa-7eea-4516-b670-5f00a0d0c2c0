package com.data.platform.datamind.server.datameta.lineage;

import com.data.platform.datamind.server.datameta.vo.lineage.ImpactAnalysisRespVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;

import java.util.List;
import java.util.Map;

/**
 * 影响分析器接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @datetime 2025/7/5
 * @project datamind-cloud-mini-service
 * @package com.data.platform.datamind.server.datameta.lineage
 * @description 影响分析器，提供数据变更影响分析功能
 * @since 1.8
 */
public interface ImpactAnalyzer {

    /**
     * 分析表变更的影响
     *
     * @param tableId    表ID
     * @param changeType 变更类型（CREATE, UPDATE, DELETE, RENAME等）
     * @return 影响分析结果
     */
    ImpactAnalysisRespVO analyzeTableImpact(Long tableId, String changeType);

    /**
     * 分析列变更的影响
     *
     * @param columnId   列ID
     * @param changeType 变更类型（CREATE, UPDATE, DELETE, RENAME, TYPE_CHANGE等）
     * @return 影响分析结果
     */
    ImpactAnalysisRespVO analyzeColumnImpact(Long columnId, String changeType);

    /**
     * 分析表删除的影响
     *
     * @param tableId 表ID
     * @return 删除影响分析结果
     */
    ImpactAnalysisRespVO analyzeTableDeletionImpact(Long tableId);

    /**
     * 分析列删除的影响
     *
     * @param columnId 列ID
     * @return 删除影响分析结果
     */
    ImpactAnalysisRespVO analyzeColumnDeletionImpact(Long columnId);

    /**
     * 分析表重命名的影响
     *
     * @param tableId 表ID
     * @param newName 新表名
     * @return 重命名影响分析结果
     */
    ImpactAnalysisRespVO analyzeTableRenameImpact(Long tableId, String newName);

    /**
     * 分析列重命名的影响
     *
     * @param columnId 列ID
     * @param newName  新列名
     * @return 重命名影响分析结果
     */
    ImpactAnalysisRespVO analyzeColumnRenameImpact(Long columnId, String newName);

    /**
     * 分析列类型变更的影响
     *
     * @param columnId 列ID
     * @param newType  新数据类型
     * @return 类型变更影响分析结果
     */
    ImpactAnalysisRespVO analyzeColumnTypeChangeImpact(Long columnId, String newType);

    /**
     * 获取受影响的下游表
     *
     * @param tableId 表ID
     * @param depth   影响深度
     * @return 受影响的下游表列表
     */
    List<LineageNodeVO> getImpactedDownstreamTables(Long tableId, Integer depth);

    /**
     * 获取受影响的下游列
     *
     * @param columnId 列ID
     * @param depth    影响深度
     * @return 受影响的下游列列表
     */
    List<LineageNodeVO> getImpactedDownstreamColumns(Long columnId, Integer depth);

    /**
     * 计算影响范围评分
     *
     * @param tableId 表ID
     * @return 影响范围评分（0-100）
     */
    Integer calculateImpactScore(Long tableId);

    /**
     * 计算列影响范围评分
     *
     * @param columnId 列ID
     * @return 影响范围评分（0-100）
     */
    Integer calculateColumnImpactScore(Long columnId);

    /**
     * 获取影响分析统计信息
     *
     * @param tableId 表ID
     * @return 影响分析统计信息
     */
    Map<String, Object> getImpactStatistics(Long tableId);

    /**
     * 获取数据库级别的影响分析统计
     *
     * @param databaseId 数据库ID
     * @return 数据库影响分析统计信息
     */
    Map<String, Object> getDatabaseImpactStatistics(Long databaseId);

    /**
     * 分析批量变更的影响
     *
     * @param changes 变更列表
     * @return 批量变更影响分析结果
     */
    Map<String, Object> analyzeBatchChangesImpact(List<Map<String, Object>> changes);

    /**
     * 预测变更的风险等级
     *
     * @param tableId    表ID
     * @param changeType 变更类型
     * @return 风险等级（LOW, MEDIUM, HIGH, CRITICAL）
     */
    String predictChangeRiskLevel(Long tableId, String changeType);

    /**
     * 生成影响分析报告
     *
     * @param tableId 表ID
     * @return 影响分析报告
     */
    Map<String, Object> generateImpactReport(Long tableId);

    /**
     * 获取变更建议
     *
     * @param tableId    表ID
     * @param changeType 变更类型
     * @return 变更建议列表
     */
    List<String> getChangeRecommendations(Long tableId, String changeType);

    /**
     * 分析变更的时间窗口
     *
     * @param tableId 表ID
     * @return 最佳变更时间窗口建议
     */
    Map<String, Object> analyzeChangeTimeWindow(Long tableId);

    /**
     * 获取影响链路分析
     *
     * @param tableId 表ID
     * @param depth   分析深度
     * @return 影响链路分析结果
     */
    Map<String, Object> getImpactChainAnalysis(Long tableId, Integer depth);

    /**
     * 验证影响分析的准确性
     *
     * @param tableId 表ID
     * @return 验证结果
     */
    Map<String, Object> validateImpactAnalysis(Long tableId);
}
