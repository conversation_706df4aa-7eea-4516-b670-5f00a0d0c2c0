package com.data.platform.datamind.server.datameta.lineage.impl;

import com.data.platform.datamind.server.datameta.graph.GraphQueryService;
import com.data.platform.datamind.server.datameta.lineage.DataFlowTracker;
import com.data.platform.datamind.server.datameta.lineage.LineageAnalyzer;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.vo.lineage.DataFlowPathVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据流追踪器实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class DataFlowTrackerImpl implements DataFlowTracker {

    @Resource
    private GraphQueryService graphQueryService;

    @Resource
    private LineageAnalyzer lineageAnalyzer;

    @Resource
    private MetaTableService metaTableService;

    private static final Integer DEFAULT_DEPTH = 3;
    private static final Integer MAX_DEPTH = 10;

    @Override
    public DataFlowPathVO trackTableDataFlow(Long sourceTableId, Long targetTableId) {
        log.info("[DataFlowTracker] Tracking table data flow from {} to {}", sourceTableId, targetTableId);

        try {
            List<LineageNodeVO> pathNodes = lineageAnalyzer.analyzeLineagePath(sourceTableId, targetTableId);
            
            if (pathNodes.isEmpty()) {
                log.warn("[DataFlowTracker] No data flow path found from table {} to table {}", sourceTableId, targetTableId);
                return null;
            }

            return buildDataFlowPath(sourceTableId, targetTableId, pathNodes, "TABLE");

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error tracking table data flow from {} to {}", sourceTableId, targetTableId, e);
            return null;
        }
    }

    @Override
    public DataFlowPathVO trackColumnDataFlow(Long sourceColumnId, Long targetColumnId) {
        log.info("[DataFlowTracker] Tracking column data flow from {} to {}", sourceColumnId, targetColumnId);

        try {
            List<LineageNodeVO> pathNodes = lineageAnalyzer.analyzeColumnLineagePath(sourceColumnId, targetColumnId);
            
            if (pathNodes.isEmpty()) {
                log.warn("[DataFlowTracker] No data flow path found from column {} to column {}", sourceColumnId, targetColumnId);
                return null;
            }

            return buildDataFlowPath(sourceColumnId, targetColumnId, pathNodes, "COLUMN");

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error tracking column data flow from {} to {}", sourceColumnId, targetColumnId, e);
            return null;
        }
    }

    @Override
    public List<DataFlowPathVO> getDataInflowPaths(Long tableId, Integer depth) {
        log.info("[DataFlowTracker] Getting data inflow paths for tableId: {}, depth: {}", tableId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            List<LineageNodeVO> upstreamTables = lineageAnalyzer.getUpstreamTables(tableId, depth);
            
            return upstreamTables.stream()
                    .map(upstream -> trackTableDataFlow(upstream.getId(), tableId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error getting data inflow paths for tableId: {}", tableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DataFlowPathVO> getDataOutflowPaths(Long tableId, Integer depth) {
        log.info("[DataFlowTracker] Getting data outflow paths for tableId: {}, depth: {}", tableId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            List<LineageNodeVO> downstreamTables = lineageAnalyzer.getDownstreamTables(tableId, depth);
            
            return downstreamTables.stream()
                    .map(downstream -> trackTableDataFlow(tableId, downstream.getId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error getting data outflow paths for tableId: {}", tableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DataFlowPathVO> getColumnInflowPaths(Long columnId, Integer depth) {
        log.info("[DataFlowTracker] Getting column inflow paths for columnId: {}, depth: {}", columnId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            List<LineageNodeVO> upstreamColumns = lineageAnalyzer.getUpstreamColumns(columnId, depth);
            
            return upstreamColumns.stream()
                    .map(upstream -> trackColumnDataFlow(upstream.getId(), columnId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error getting column inflow paths for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DataFlowPathVO> getColumnOutflowPaths(Long columnId, Integer depth) {
        log.info("[DataFlowTracker] Getting column outflow paths for columnId: {}, depth: {}", columnId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            List<LineageNodeVO> downstreamColumns = lineageAnalyzer.getDownstreamColumns(columnId, depth);
            
            return downstreamColumns.stream()
                    .map(downstream -> trackColumnDataFlow(columnId, downstream.getId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error getting column outflow paths for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> analyzeDataFlowComplexity(Long tableId) {
        log.info("[DataFlowTracker] Analyzing data flow complexity for tableId: {}", tableId);

        try {
            Map<String, Object> complexity = new HashMap<>();

            List<DataFlowPathVO> inflowPaths = getDataInflowPaths(tableId, DEFAULT_DEPTH);
            List<DataFlowPathVO> outflowPaths = getDataOutflowPaths(tableId, DEFAULT_DEPTH);

            int totalPaths = inflowPaths.size() + outflowPaths.size();
            int maxPathLength = Math.max(
                    inflowPaths.stream().mapToInt(path -> path.getPathLength() != null ? path.getPathLength() : 0).max().orElse(0),
                    outflowPaths.stream().mapToInt(path -> path.getPathLength() != null ? path.getPathLength() : 0).max().orElse(0)
            );

            complexity.put("totalPaths", totalPaths);
            complexity.put("inflowPaths", inflowPaths.size());
            complexity.put("outflowPaths", outflowPaths.size());
            complexity.put("maxPathLength", maxPathLength);
            complexity.put("complexityLevel", calculateComplexityLevel(totalPaths, maxPathLength));
            complexity.put("isDataHub", totalPaths > 10); // 判断是否为数据枢纽

            return complexity;

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error analyzing data flow complexity for tableId: {}", tableId, e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getDataFlowStatistics(Long tableId) {
        log.info("[DataFlowTracker] Getting data flow statistics for tableId: {}", tableId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            List<DataFlowPathVO> inflowPaths = getDataInflowPaths(tableId, DEFAULT_DEPTH);
            List<DataFlowPathVO> outflowPaths = getDataOutflowPaths(tableId, DEFAULT_DEPTH);

            statistics.put("totalInflowPaths", inflowPaths.size());
            statistics.put("totalOutflowPaths", outflowPaths.size());
            statistics.put("totalDataFlowPaths", inflowPaths.size() + outflowPaths.size());

            // 计算路径质量统计
            double avgInflowQuality = inflowPaths.stream()
                    .mapToInt(path -> path.getQualityScore() != null ? path.getQualityScore() : 0)
                    .average().orElse(0.0);
            double avgOutflowQuality = outflowPaths.stream()
                    .mapToInt(path -> path.getQualityScore() != null ? path.getQualityScore() : 0)
                    .average().orElse(0.0);

            statistics.put("averageInflowQuality", avgInflowQuality);
            statistics.put("averageOutflowQuality", avgOutflowQuality);
            statistics.put("overallQuality", (avgInflowQuality + avgOutflowQuality) / 2);

            // 计算关键路径数量
            long criticalInflowPaths = inflowPaths.stream()
                    .filter(path -> path.getIsCriticalPath() != null && path.getIsCriticalPath())
                    .count();
            long criticalOutflowPaths = outflowPaths.stream()
                    .filter(path -> path.getIsCriticalPath() != null && path.getIsCriticalPath())
                    .count();

            statistics.put("criticalInflowPaths", criticalInflowPaths);
            statistics.put("criticalOutflowPaths", criticalOutflowPaths);
            statistics.put("totalCriticalPaths", criticalInflowPaths + criticalOutflowPaths);

            return statistics;

        } catch (Exception e) {
            log.error("[DataFlowTracker] Error getting data flow statistics for tableId: {}", tableId, e);
            return new HashMap<>();
        }
    }

    // 私有辅助方法
    private DataFlowPathVO buildDataFlowPath(Long sourceId, Long targetId, List<LineageNodeVO> pathNodes, String pathType) {
        if (pathNodes.isEmpty()) {
            return null;
        }

        LineageNodeVO sourceNode = pathNodes.get(0);
        LineageNodeVO targetNode = pathNodes.get(pathNodes.size() - 1);

        return DataFlowPathVO.builder()
                .pathId(generatePathId(sourceId, targetId))
                .sourceNode(sourceNode)
                .targetNode(targetNode)
                .pathNodes(pathNodes)
                .pathLength(pathNodes.size())
                .pathType(pathType)
                .pathStatus("ACTIVE")
                .flowDirection("DOWNSTREAM")
                .flowStrength(calculateFlowStrength(pathNodes))
                .qualityScore(calculatePathQuality(pathNodes))
                .complexity(calculatePathComplexity(pathNodes.size()))
                .isCriticalPath(pathNodes.size() <= 2) // 短路径认为是关键路径
                .createTime(new Date().toString())
                .updateTime(new Date().toString())
                .build();
    }

    private String generatePathId(Long sourceId, Long targetId) {
        return String.format("path_%d_%d_%d", sourceId, targetId, System.currentTimeMillis());
    }

    private String calculateFlowStrength(List<LineageNodeVO> pathNodes) {
        if (pathNodes.size() <= 2) return "HIGH";
        if (pathNodes.size() <= 4) return "MEDIUM";
        return "LOW";
    }

    private Integer calculatePathQuality(List<LineageNodeVO> pathNodes) {
        // 基于路径长度和节点质量计算路径质量评分
        int baseScore = 100;
        int lengthPenalty = Math.max(0, (pathNodes.size() - 2) * 10);
        return Math.max(0, baseScore - lengthPenalty);
    }

    private String calculatePathComplexity(int pathLength) {
        if (pathLength <= 2) return "LOW";
        if (pathLength <= 4) return "MEDIUM";
        if (pathLength <= 6) return "HIGH";
        return "VERY_HIGH";
    }

    private String calculateComplexityLevel(int totalPaths, int maxPathLength) {
        int complexityScore = totalPaths + maxPathLength;
        if (complexityScore <= 5) return "LOW";
        if (complexityScore <= 15) return "MEDIUM";
        if (complexityScore <= 30) return "HIGH";
        return "VERY_HIGH";
    }

    // 其他方法的简化实现，可以后续完善
    @Override
    public List<Map<String, Object>> detectDataFlowBottlenecks(Long databaseId) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> analyzeDataFlowQuality(Long tableId) {
        return getDataFlowStatistics(tableId);
    }

    @Override
    public List<Map<String, Object>> getDataFlowHotspots(Long databaseId, Integer limit) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> trackDataLifecycle(Long tableId) {
        Map<String, Object> lifecycle = new HashMap<>();
        lifecycle.put("tableId", tableId);
        lifecycle.put("lifecycleStage", "ACTIVE");
        return lifecycle;
    }

    @Override
    public Map<String, Object> analyzeDataFlowTiming(Long tableId) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getDataFlowDependencyStrength(Long sourceTableId, Long targetTableId) {
        Map<String, Object> strength = new HashMap<>();
        strength.put("dependencyStrength", "MEDIUM");
        strength.put("strengthScore", 75);
        return strength;
    }

    @Override
    public Map<String, Object> predictDataFlowImpact(Long tableId) {
        return analyzeDataFlowComplexity(tableId);
    }

    @Override
    public List<String> suggestDataFlowOptimization(Long tableId) {
        List<String> suggestions = new ArrayList<>();
        suggestions.add("考虑优化数据流路径，减少中间节点");
        suggestions.add("建议添加数据质量检查点");
        suggestions.add("可以考虑建立数据缓存层");
        return suggestions;
    }
}
