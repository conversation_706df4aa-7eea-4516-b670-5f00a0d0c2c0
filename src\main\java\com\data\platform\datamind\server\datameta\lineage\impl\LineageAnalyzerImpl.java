package com.data.platform.datamind.server.datameta.lineage.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.graph.GraphQueryService;
import com.data.platform.datamind.server.datameta.lineage.LineageAnalyzer;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 血缘分析器实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class LineageAnalyzerImpl implements LineageAnalyzer {

    @Resource
    private GraphQueryService graphQueryService;

    @Resource
    private MetaTableService metaTableService;

    @Resource
    private MetaColumnService metaColumnService;

    private static final Integer DEFAULT_DEPTH = 3;
    private static final Integer MAX_DEPTH = 10;

    @Override
    public TableLineageRespVO analyzeTableLineage(Long tableId) {
        return analyzeTableLineage(tableId, DEFAULT_DEPTH);
    }

    @Override
    public TableLineageRespVO analyzeTableLineage(Long tableId, Integer depth) {
        log.info("[LineageAnalyzer] Analyzing table lineage for tableId: {}, depth: {}", tableId, depth);

        try {
            MetaTableDO table = metaTableService.getTable(tableId);
            if (table == null) {
                log.warn("[LineageAnalyzer] Table not found: {}", tableId);
                return new TableLineageRespVO();
            }

            TableLineageRespVO respVO = new TableLineageRespVO();

            // 设置目标表信息
            TableLineageRespVO.TableInfo targetTable = buildTableInfo(table);
            respVO.setTargetTable(targetTable);

            // 获取上游表
            List<LineageNodeVO> upstreamNodes = getUpstreamTables(tableId, depth);
            List<TableLineageRespVO.TableInfo> upstreamTables = convertToTableInfoList(upstreamNodes);
            respVO.setUpstreamTables(upstreamTables);

            // 获取下游表
            List<LineageNodeVO> downstreamNodes = getDownstreamTables(tableId, depth);
            List<TableLineageRespVO.TableInfo> downstreamTables = convertToTableInfoList(downstreamNodes);
            respVO.setDownstreamTables(downstreamTables);

            // 构建血缘关系
            List<TableLineageRespVO.LineageRelation> relations = buildLineageRelations(tableId, upstreamNodes, downstreamNodes);
            respVO.setRelations(relations);

            log.info("[LineageAnalyzer] Table lineage analysis completed for tableId: {}", tableId);
            return respVO;

        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error analyzing table lineage for tableId: {}", tableId, e);
            return new TableLineageRespVO();
        }
    }

    @Override
    public List<LineageNodeVO> analyzeColumnLineage(Long columnId) {
        return analyzeColumnLineage(columnId, DEFAULT_DEPTH);
    }

    @Override
    public List<LineageNodeVO> analyzeColumnLineage(Long columnId, Integer depth) {
        log.info("[LineageAnalyzer] Analyzing column lineage for columnId: {}, depth: {}", columnId, depth);

        try {
            return graphQueryService.getColumnLineage(columnId);
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error analyzing column lineage for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> getUpstreamTables(Long tableId, Integer depth) {
        log.info("[LineageAnalyzer] Getting upstream tables for tableId: {}, depth: {}", tableId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return graphQueryService.getUpstreamTables(tableId, depth)
                    .stream()
                    .map(this::convertToLineageNode)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting upstream tables for tableId: {}", tableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> getDownstreamTables(Long tableId, Integer depth) {
        log.info("[LineageAnalyzer] Getting downstream tables for tableId: {}, depth: {}", tableId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return graphQueryService.getDownstreamTables(tableId, depth)
                    .stream()
                    .map(this::convertToLineageNode)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting downstream tables for tableId: {}", tableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> getUpstreamColumns(Long columnId, Integer depth) {
        log.info("[LineageAnalyzer] Getting upstream columns for columnId: {}, depth: {}", columnId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return graphQueryService.getUpstreamColumns(columnId, depth);
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting upstream columns for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> getDownstreamColumns(Long columnId, Integer depth) {
        log.info("[LineageAnalyzer] Getting downstream columns for columnId: {}, depth: {}", columnId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return graphQueryService.getDownstreamColumns(columnId, depth);
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting downstream columns for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> analyzeLineagePath(Long sourceTableId, Long targetTableId) {
        log.info("[LineageAnalyzer] Analyzing lineage path from {} to {}", sourceTableId, targetTableId);

        try {
            return graphQueryService.getLineagePath(sourceTableId, targetTableId);
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error analyzing lineage path from {} to {}", sourceTableId, targetTableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> analyzeColumnLineagePath(Long sourceColumnId, Long targetColumnId) {
        log.info("[LineageAnalyzer] Analyzing column lineage path from {} to {}", sourceColumnId, targetColumnId);

        try {
            return graphQueryService.getColumnLineagePath(sourceColumnId, targetColumnId);
        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error analyzing column lineage path from {} to {}", sourceColumnId, targetColumnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getLineageStatistics(Long tableId) {
        log.info("[LineageAnalyzer] Getting lineage statistics for tableId: {}", tableId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            List<LineageNodeVO> upstreamTables = getUpstreamTables(tableId, DEFAULT_DEPTH);
            List<LineageNodeVO> downstreamTables = getDownstreamTables(tableId, DEFAULT_DEPTH);

            statistics.put("upstreamCount", upstreamTables.size());
            statistics.put("downstreamCount", downstreamTables.size());
            statistics.put("totalConnections", upstreamTables.size() + downstreamTables.size());
            statistics.put("maxUpstreamDepth", calculateMaxDepth(upstreamTables));
            statistics.put("maxDownstreamDepth", calculateMaxDepth(downstreamTables));
            statistics.put("lineageComplexity", calculateLineageComplexity(upstreamTables.size(), downstreamTables.size()));

            return statistics;

        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting lineage statistics for tableId: {}", tableId, e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getDatabaseLineageStatistics(Long databaseId) {
        log.info("[LineageAnalyzer] Getting database lineage statistics for databaseId: {}", databaseId);

        try {
            Map<String, Object> statistics = new HashMap<>();
            List<MetaTableDO> tables = metaTableService.getTablesByDbId(databaseId);

            int totalTables = tables.size();
            int tablesWithUpstream = 0;
            int tablesWithDownstream = 0;
            int totalConnections = 0;

            for (MetaTableDO table : tables) {
                List<LineageNodeVO> upstream = getUpstreamTables(table.getId(), 1);
                List<LineageNodeVO> downstream = getDownstreamTables(table.getId(), 1);

                if (!upstream.isEmpty()) tablesWithUpstream++;
                if (!downstream.isEmpty()) tablesWithDownstream++;
                totalConnections += upstream.size() + downstream.size();
            }

            statistics.put("totalTables", totalTables);
            statistics.put("tablesWithUpstream", tablesWithUpstream);
            statistics.put("tablesWithDownstream", tablesWithDownstream);
            statistics.put("isolatedTables", totalTables - tablesWithUpstream - tablesWithDownstream);
            statistics.put("totalConnections", totalConnections);
            statistics.put("averageConnections", totalTables > 0 ? (double) totalConnections / totalTables : 0);
            statistics.put("connectivityRate", totalTables > 0 ? (double) (tablesWithUpstream + tablesWithDownstream) / totalTables : 0);

            return statistics;

        } catch (Exception e) {
            log.error("[LineageAnalyzer] Error getting database lineage statistics for databaseId: {}", databaseId, e);
            return new HashMap<>();
        }
    }

    // 私有辅助方法
    private TableLineageRespVO.TableInfo buildTableInfo(MetaTableDO table) {
        TableLineageRespVO.TableInfo tableInfo = new TableLineageRespVO.TableInfo();
        tableInfo.setTableId(table.getId());
        tableInfo.setTableName(table.getTableName());
        tableInfo.setTableComment(table.getTableComment());
        tableInfo.setDatabaseId(table.getDbId());
        // 可以添加更多字段
        return tableInfo;
    }

    private List<TableLineageRespVO.TableInfo> convertToTableInfoList(List<LineageNodeVO> nodes) {
        return nodes.stream()
                .filter(node -> "TABLE".equals(node.getNodeType()))
                .map(this::convertToTableInfo)
                .collect(Collectors.toList());
    }

    private TableLineageRespVO.TableInfo convertToTableInfo(LineageNodeVO node) {
        TableLineageRespVO.TableInfo tableInfo = new TableLineageRespVO.TableInfo();
        tableInfo.setTableId(node.getId());
        tableInfo.setTableName(node.getNodeName());
        tableInfo.setTableComment(node.getNodeDescription());
        tableInfo.setDatabaseId(node.getDatabaseId());
        tableInfo.setDatabaseName(node.getDatabaseName());
        return tableInfo;
    }

    private LineageNodeVO convertToLineageNode(com.data.platform.datamind.server.datameta.vo.relationship.TableRelationshipVO relationshipVO) {
        return LineageNodeVO.builder()
                .id(relationshipVO.getTableId())
                .nodeType("TABLE")
                .nodeName(relationshipVO.getTableName())
                .nodeDescription(relationshipVO.getTableComment())
                .databaseId(relationshipVO.getDatabaseId())
                .databaseName(relationshipVO.getDatabaseName())
                .build();
    }

    private List<TableLineageRespVO.LineageRelation> buildLineageRelations(Long tableId, 
                                                                          List<LineageNodeVO> upstreamNodes, 
                                                                          List<LineageNodeVO> downstreamNodes) {
        List<TableLineageRespVO.LineageRelation> relations = new ArrayList<>();
        
        // 构建上游关系
        for (LineageNodeVO upstream : upstreamNodes) {
            TableLineageRespVO.LineageRelation relation = new TableLineageRespVO.LineageRelation();
            relation.setSourceTableId(upstream.getId());
            relation.setSourceTableName(upstream.getNodeName());
            relation.setTargetTableId(tableId);
            relation.setRelationType("UPSTREAM");
            relations.add(relation);
        }
        
        // 构建下游关系
        for (LineageNodeVO downstream : downstreamNodes) {
            TableLineageRespVO.LineageRelation relation = new TableLineageRespVO.LineageRelation();
            relation.setSourceTableId(tableId);
            relation.setTargetTableId(downstream.getId());
            relation.setTargetTableName(downstream.getNodeName());
            relation.setRelationType("DOWNSTREAM");
            relations.add(relation);
        }
        
        return relations;
    }

    private Integer calculateMaxDepth(List<LineageNodeVO> nodes) {
        return nodes.stream()
                .mapToInt(node -> node.getLineageLevel() != null ? node.getLineageLevel() : 1)
                .max()
                .orElse(0);
    }

    private String calculateLineageComplexity(int upstreamCount, int downstreamCount) {
        int totalConnections = upstreamCount + downstreamCount;
        if (totalConnections <= 2) return "LOW";
        if (totalConnections <= 5) return "MEDIUM";
        if (totalConnections <= 10) return "HIGH";
        return "VERY_HIGH";
    }

    // 其他方法的简化实现，可以后续完善
    @Override
    public Map<String, Object> validateLineageIntegrity(Long tableId) {
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", true);
        result.put("issues", new ArrayList<>());
        return result;
    }

    @Override
    public List<List<LineageNodeVO>> detectLineageCycles(Long databaseId) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> analyzeLineageComplexity(Long tableId) {
        return getLineageStatistics(tableId);
    }

    @Override
    public Map<String, Object> getLineageQualityScore(Long tableId) {
        Map<String, Object> result = new HashMap<>();
        result.put("qualityScore", 85);
        result.put("qualityLevel", "GOOD");
        return result;
    }
}
