package com.data.platform.datamind.server.datameta.graph;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.graph.model.GraphNode;
import com.data.platform.datamind.server.datameta.graph.model.GraphRelationship;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图谱数据同步服务
 * 负责将关系型元数据同步到Neo4j图数据库
 *
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class GraphSyncService {

    @Resource
    private Neo4jService neo4jService;

    @Resource
    private GraphModelBuilder graphModelBuilder;

    /**
     * 全量同步所有元数据到图谱
     *
     * @param databases 数据库列表
     * @param tables 表列表
     * @param columns 列列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncAllMetaDataToGraph(List<MetaDatabaseDO> databases,
                                       List<MetaTableDO> tables,
                                       List<MetaColumnDO> columns) {
        log.info("Starting full metadata sync to graph, databases: {}, tables: {}, columns: {}", 
                databases.size(), tables.size(), columns.size());

        try {
            // 1. 初始化图谱结构
            initializeGraphSchema();

            // 2. 清空现有数据（可选，根据业务需求决定）
            // clearExistingData();

            // 3. 同步数据库节点
            syncDatabaseNodes(databases);

            // 4. 同步表节点
            syncTableNodes(tables);

            // 5. 同步列节点
            syncColumnNodes(columns);

            // 6. 同步关系
            syncRelationships(databases, tables, columns);

            log.info("Full metadata sync to graph completed successfully");
        } catch (Exception e) {
            log.error("Error during full metadata sync to graph", e);
            throw new RuntimeException("图谱数据同步失败", e);
        }
    }

    /**
     * 增量同步数据库节点
     *
     * @param databases 数据库列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncDatabaseNodes(List<MetaDatabaseDO> databases) {
        log.info("Syncing {} database nodes", databases.size());

        List<String> cypherList = new ArrayList<>();
        List<Map<String, Object>> parametersList = new ArrayList<>();

        for (MetaDatabaseDO database : databases) {
            GraphNode node = graphModelBuilder.buildDatabaseNode(database);
            String cypher = buildNodeMergeCypher("Database", node.getProperties());
            cypherList.add(cypher);
            parametersList.add(node.getProperties());
        }

        neo4jService.executeBatchWrite(cypherList, parametersList);
        log.info("Database nodes sync completed");
    }

    /**
     * 增量同步表节点
     *
     * @param tables 表列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTableNodes(List<MetaTableDO> tables) {
        log.info("Syncing {} table nodes", tables.size());

        List<String> cypherList = new ArrayList<>();
        List<Map<String, Object>> parametersList = new ArrayList<>();

        for (MetaTableDO table : tables) {
            GraphNode node = graphModelBuilder.buildTableNode(table);
            String cypher = buildNodeMergeCypher("Table", node.getProperties());
            cypherList.add(cypher);
            parametersList.add(node.getProperties());
        }

        neo4jService.executeBatchWrite(cypherList, parametersList);
        log.info("Table nodes sync completed");
    }

    /**
     * 增量同步列节点
     *
     * @param columns 列列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncColumnNodes(List<MetaColumnDO> columns) {
        log.info("Syncing {} column nodes", columns.size());

        List<String> cypherList = new ArrayList<>();
        List<Map<String, Object>> parametersList = new ArrayList<>();

        for (MetaColumnDO column : columns) {
            GraphNode node = graphModelBuilder.buildColumnNode(column);
            String cypher = buildNodeMergeCypher("Column", node.getProperties());
            cypherList.add(cypher);
            parametersList.add(node.getProperties());
        }

        neo4jService.executeBatchWrite(cypherList, parametersList);
        log.info("Column nodes sync completed");
    }

    /**
     * 同步关系
     *
     * @param databases 数据库列表
     * @param tables 表列表
     * @param columns 列列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncRelationships(List<MetaDatabaseDO> databases,
                                  List<MetaTableDO> tables,
                                  List<MetaColumnDO> columns) {
        log.info("Syncing relationships");

        List<String> cypherList = new ArrayList<>();
        List<Map<String, Object>> parametersList = new ArrayList<>();

        // 同步数据库-表关系
        for (MetaTableDO table : tables) {
            GraphRelationship relationship = graphModelBuilder.buildDatabaseTableRelationship(table.getDbId(), table.getId());
            String cypher = buildRelationshipMergeCypher(relationship);
            Map<String, Object> params = new HashMap<>();
            params.put("sourceId", relationship.getSourceNodeId());
            params.put("targetId", relationship.getTargetNodeId());
            params.putAll(relationship.getProperties());
            
            cypherList.add(cypher);
            parametersList.add(params);
        }

        // 同步表-列关系
        for (MetaColumnDO column : columns) {
            GraphRelationship relationship = graphModelBuilder.buildTableColumnRelationship(column.getTableId(), column.getId());
            String cypher = buildRelationshipMergeCypher(relationship);
            Map<String, Object> params = new HashMap<>();
            params.put("sourceId", relationship.getSourceNodeId());
            params.put("targetId", relationship.getTargetNodeId());
            params.putAll(relationship.getProperties());
            
            cypherList.add(cypher);
            parametersList.add(params);
        }

        // 同步外键关系
        for (MetaColumnDO column : columns) {
            if (Boolean.TRUE.equals(column.getIsForeignKey()) && column.getFkColumnId() != null) {
                GraphRelationship relationship = graphModelBuilder.buildForeignKeyRelationship(column.getId(), column.getFkColumnId());
                String cypher = buildRelationshipMergeCypher(relationship);
                Map<String, Object> params = new HashMap<>();
                params.put("sourceId", relationship.getSourceNodeId());
                params.put("targetId", relationship.getTargetNodeId());
                params.putAll(relationship.getProperties());
                
                cypherList.add(cypher);
                parametersList.add(params);
            }
        }

        neo4jService.executeBatchWrite(cypherList, parametersList);
        log.info("Relationships sync completed");
    }

    /**
     * 初始化图谱结构（创建索引和约束）
     */
    private void initializeGraphSchema() {
        log.info("Initializing graph schema");

        // 创建唯一约束
        neo4jService.createUniqueConstraint("Database", "id");
        neo4jService.createUniqueConstraint("Table", "id");
        neo4jService.createUniqueConstraint("Column", "id");

        // 创建索引
        neo4jService.createIndex("Database", "name");
        neo4jService.createIndex("Database", "type");
        neo4jService.createIndex("Table", "tableName");
        neo4jService.createIndex("Table", "dbId");
        neo4jService.createIndex("Column", "columnName");
        neo4jService.createIndex("Column", "tableId");

        log.info("Graph schema initialization completed");
    }

    /**
     * 清空现有数据
     */
    private void clearExistingData() {
        log.info("Clearing existing graph data");
        neo4jService.clearNodesByLabel("Database");
        neo4jService.clearNodesByLabel("Table");
        neo4jService.clearNodesByLabel("Column");
        log.info("Existing graph data cleared");
    }

    /**
     * 构建节点MERGE Cypher语句
     *
     * @param label 节点标签
     * @param properties 节点属性
     * @return Cypher语句
     */
    private String buildNodeMergeCypher(String label, Map<String, Object> properties) {
        StringBuilder cypher = new StringBuilder();
        cypher.append("MERGE (n:").append(label).append(" {id: $id}) ");
        cypher.append("SET ");
        
        boolean first = true;
        for (String key : properties.keySet()) {
            if (!first) {
                cypher.append(", ");
            }
            cypher.append("n.").append(key).append(" = $").append(key);
            first = false;
        }
        
        return cypher.toString();
    }

    /**
     * 构建关系MERGE Cypher语句
     *
     * @param relationship 关系对象
     * @return Cypher语句
     */
    private String buildRelationshipMergeCypher(GraphRelationship relationship) {
        String[] labels = getNodeLabelsForRelationship(relationship);
        String sourceLabel = labels[0];
        String targetLabel = labels[1];

        StringBuilder cypher = new StringBuilder();
        cypher.append("MATCH (source:").append(sourceLabel).append(" {id: $sourceId}), ");
        cypher.append("(target:").append(targetLabel).append(" {id: $targetId}) ");
        cypher.append("MERGE (source)-[r:").append(relationship.getType()).append("]->(target) ");

        if (relationship.getProperties() != null && !relationship.getProperties().isEmpty()) {
            cypher.append("SET ");
            boolean first = true;
            for (String key : relationship.getProperties().keySet()) {
                if (!first) {
                    cypher.append(", ");
                }
                cypher.append("r.").append(key).append(" = $").append(key);
                first = false;
            }
        }

        return cypher.toString();
    }

    /**
     * 根据关系类型推断节点标签
     *
     * @param relationship 关系对象
     * @return 源节点和目标节点的标签数组 [sourceLabel, targetLabel]
     */
    private String[] getNodeLabelsForRelationship(GraphRelationship relationship) {
        String relationType = relationship.getType();

        switch (relationType) {
            case "CONTAINS_TABLE":
                return new String[]{"Database", "Table"};
            case "HAS_COLUMN":
                return new String[]{"Table", "Column"};
            case "REFERENCES":
                return new String[]{"Column", "Column"};
            case "RELATED_TO":
                return new String[]{"Table", "Table"};
            default:
                return new String[]{"Node", "Node"};
        }
    }
}
