package com.data.platform.datamind.server.datameta.graph;

import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Neo4j 基础服务类
 * 提供Neo4j数据库的基础操作功能
 *
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class Neo4jService {

    @Resource
    private Neo4jClient neo4jClient;

    @Resource
    private Neo4jTemplate neo4jTemplate;

    /**
     * 执行Cypher查询语句
     *
     * @param cypher Cypher查询语句
     * @param parameters 查询参数
     * @return 查询结果
     */
    public List<Map<String, Object>> executeQuery(String cypher, Map<String, Object> parameters) {
        try {
            log.debug("Executing Cypher query: {}", cypher);
            return neo4jClient.query(cypher)
                    .bindAll(parameters)
                    .fetch()
                    .all();
        } catch (Exception e) {
            log.error("Error executing Cypher query: {}", cypher, e);
            throw new RuntimeException("Neo4j查询执行失败", e);
        }
    }

    /**
     * 执行Cypher写操作语句
     *
     * @param cypher Cypher写操作语句
     * @param parameters 操作参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeWrite(String cypher, Map<String, Object> parameters) {
        try {
            log.debug("Executing Cypher write operation: {}", cypher);
            neo4jClient.query(cypher)
                    .bindAll(parameters)
                    .run();
        } catch (Exception e) {
            log.error("Error executing Cypher write operation: {}", cypher, e);
            throw new RuntimeException("Neo4j写操作执行失败", e);
        }
    }

    /**
     * 批量执行Cypher写操作
     *
     * @param cypherList Cypher语句列表
     * @param parametersList 参数列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeBatchWrite(List<String> cypherList, List<Map<String, Object>> parametersList) {
        if (cypherList.size() != parametersList.size()) {
            throw new IllegalArgumentException("Cypher语句数量与参数数量不匹配");
        }

        try {
            for (int i = 0; i < cypherList.size(); i++) {
                String cypher = cypherList.get(i);
                Map<String, Object> parameters = parametersList.get(i);
                neo4jClient.query(cypher)
                        .bindAll(parameters)
                        .run();
            }
            log.info("Batch write operations completed successfully, total: {}", cypherList.size());
        } catch (Exception e) {
            log.error("Error executing batch write operations", e);
            throw new RuntimeException("Neo4j批量写操作执行失败", e);
        }
    }

    /**
     * 清空指定标签的所有节点
     *
     * @param label 节点标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearNodesByLabel(String label) {
        String cypher = "MATCH (n:" + label + ") DETACH DELETE n";
        try {
            neo4jClient.query(cypher).run();
            log.info("Cleared all nodes with label: {}", label);
        } catch (Exception e) {
            log.error("Error clearing nodes with label: {}", label, e);
            throw new RuntimeException("清空Neo4j节点失败", e);
        }
    }

    /**
     * 获取数据库连接状态
     *
     * @return 连接是否正常
     */
    public boolean isConnected() {
        try {
            neo4jClient.query("RETURN 1").fetch().one();
            return true;
        } catch (Exception e) {
            log.warn("Neo4j connection check failed", e);
            return false;
        }
    }

    /**
     * 获取节点数量
     *
     * @param label 节点标签
     * @return 节点数量
     */
    public long getNodeCount(String label) {
        String cypher = "MATCH (n:" + label + ") RETURN count(n) as count";
        try {
            Map<String, Object> result = neo4jClient.query(cypher).fetch().one();
            return result != null ? (Long) result.get("count") : 0L;
        } catch (Exception e) {
            log.error("Error getting node count for label: {}", label, e);
            return 0L;
        }
    }

    /**
     * 获取关系数量
     *
     * @param relationshipType 关系类型
     * @return 关系数量
     */
    public long getRelationshipCount(String relationshipType) {
        String cypher = "MATCH ()-[r:" + relationshipType + "]-() RETURN count(r) as count";
        try {
            Map<String, Object> result = neo4jClient.query(cypher).fetch().one();
            return result != null ? (Long) result.get("count") : 0L;
        } catch (Exception e) {
            log.error("Error getting relationship count for type: {}", relationshipType, e);
            return 0L;
        }
    }

    /**
     * 创建索引
     *
     * @param label 节点标签
     * @param property 属性名
     */
    @Transactional(rollbackFor = Exception.class)
    public void createIndex(String label, String property) {
        String cypher = "CREATE INDEX IF NOT EXISTS FOR (n:" + label + ") ON (n." + property + ")";
        try {
            neo4jClient.query(cypher).run();
            log.info("Created index for {}:{}", label, property);
        } catch (Exception e) {
            log.error("Error creating index for {}:{}", label, property, e);
            throw new RuntimeException("创建Neo4j索引失败", e);
        }
    }

    /**
     * 创建唯一约束
     *
     * @param label 节点标签
     * @param property 属性名
     */
    @Transactional(rollbackFor = Exception.class)
    public void createUniqueConstraint(String label, String property) {
        String cypher = "CREATE CONSTRAINT IF NOT EXISTS FOR (n:" + label + ") REQUIRE n." + property + " IS UNIQUE";
        try {
            neo4jClient.query(cypher).run();
            log.info("Created unique constraint for {}:{}", label, property);
        } catch (Exception e) {
            log.error("Error creating unique constraint for {}:{}", label, property, e);
            throw new RuntimeException("创建Neo4j唯一约束失败", e);
        }
    }
}
