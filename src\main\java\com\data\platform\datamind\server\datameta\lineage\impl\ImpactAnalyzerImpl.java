package com.data.platform.datamind.server.datameta.lineage.impl;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.lineage.ImpactAnalyzer;
import com.data.platform.datamind.server.datameta.lineage.LineageAnalyzer;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.vo.lineage.ImpactAnalysisRespVO;
import com.data.platform.datamind.server.datameta.vo.lineage.LineageNodeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 影响分析器实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class ImpactAnalyzerImpl implements ImpactAnalyzer {

    @Resource
    private LineageAnalyzer lineageAnalyzer;

    @Resource
    private MetaTableService metaTableService;

    @Resource
    private MetaColumnService metaColumnService;

    private static final Integer DEFAULT_DEPTH = 3;
    private static final Integer MAX_DEPTH = 10;

    @Override
    public ImpactAnalysisRespVO analyzeTableImpact(Long tableId, String changeType) {
        log.info("[ImpactAnalyzer] Analyzing table impact for tableId: {}, changeType: {}", tableId, changeType);

        try {
            MetaTableDO table = metaTableService.getTable(tableId);
            if (table == null) {
                log.warn("[ImpactAnalyzer] Table not found: {}", tableId);
                return new ImpactAnalysisRespVO();
            }

            ImpactAnalysisRespVO respVO = new ImpactAnalysisRespVO();
            respVO.setAnalysisId(generateAnalysisId(tableId, changeType));
            respVO.setChangeType(changeType);
            respVO.setAnalysisTime(new Date().toString());

            // 设置目标对象信息
            ImpactAnalysisRespVO.TargetObject targetObject = buildTargetObject(table);
            respVO.setTargetObject(targetObject);

            // 获取受影响的下游对象
            List<LineageNodeVO> directImpacted = getImpactedDownstreamTables(tableId, 1);
            List<LineageNodeVO> indirectImpacted = getImpactedDownstreamTables(tableId, DEFAULT_DEPTH);
            indirectImpacted.removeAll(directImpacted); // 移除直接影响的对象

            respVO.setDirectImpactedObjects(directImpacted);
            respVO.setIndirectImpactedObjects(indirectImpacted);

            // 计算影响统计信息
            ImpactAnalysisRespVO.ImpactStatistics statistics = buildImpactStatistics(directImpacted, indirectImpacted);
            respVO.setImpactStatistics(statistics);

            // 计算影响评分和等级
            Integer impactScore = calculateImpactScore(tableId);
            respVO.setImpactScore(impactScore);
            respVO.setImpactLevel(calculateImpactLevel(impactScore));
            respVO.setRiskLevel(predictChangeRiskLevel(tableId, changeType));

            // 生成变更建议
            List<String> recommendations = getChangeRecommendations(tableId, changeType);
            respVO.setChangeRecommendations(recommendations);

            // 生成风险缓解措施
            List<String> mitigationActions = generateRiskMitigationActions(changeType, impactScore);
            respVO.setRiskMitigationActions(mitigationActions);

            // 分析最佳变更时间窗口
            ImpactAnalysisRespVO.TimeWindow timeWindow = analyzeOptimalChangeWindow(tableId);
            respVO.setBestChangeWindow(timeWindow);

            // 构建分析详情
            Map<String, Object> analysisDetails = buildAnalysisDetails(tableId, changeType, directImpacted, indirectImpacted);
            respVO.setAnalysisDetails(analysisDetails);

            log.info("[ImpactAnalyzer] Table impact analysis completed for tableId: {}", tableId);
            return respVO;

        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error analyzing table impact for tableId: {}", tableId, e);
            return new ImpactAnalysisRespVO();
        }
    }

    @Override
    public ImpactAnalysisRespVO analyzeColumnImpact(Long columnId, String changeType) {
        log.info("[ImpactAnalyzer] Analyzing column impact for columnId: {}, changeType: {}", columnId, changeType);

        try {
            MetaColumnDO column = metaColumnService.getColumn(columnId);
            if (column == null) {
                log.warn("[ImpactAnalyzer] Column not found: {}", columnId);
                return new ImpactAnalysisRespVO();
            }

            ImpactAnalysisRespVO respVO = new ImpactAnalysisRespVO();
            respVO.setAnalysisId(generateAnalysisId(columnId, changeType));
            respVO.setChangeType(changeType);
            respVO.setAnalysisTime(new Date().toString());

            // 设置目标对象信息
            ImpactAnalysisRespVO.TargetObject targetObject = buildColumnTargetObject(column);
            respVO.setTargetObject(targetObject);

            // 获取受影响的下游列
            List<LineageNodeVO> directImpacted = getImpactedDownstreamColumns(columnId, 1);
            List<LineageNodeVO> indirectImpacted = getImpactedDownstreamColumns(columnId, DEFAULT_DEPTH);
            indirectImpacted.removeAll(directImpacted);

            respVO.setDirectImpactedObjects(directImpacted);
            respVO.setIndirectImpactedObjects(indirectImpacted);

            // 计算影响统计信息
            ImpactAnalysisRespVO.ImpactStatistics statistics = buildColumnImpactStatistics(directImpacted, indirectImpacted);
            respVO.setImpactStatistics(statistics);

            // 计算影响评分和等级
            Integer impactScore = calculateColumnImpactScore(columnId);
            respVO.setImpactScore(impactScore);
            respVO.setImpactLevel(calculateImpactLevel(impactScore));
            respVO.setRiskLevel(predictColumnChangeRiskLevel(columnId, changeType));

            // 生成变更建议
            List<String> recommendations = getColumnChangeRecommendations(columnId, changeType);
            respVO.setChangeRecommendations(recommendations);

            log.info("[ImpactAnalyzer] Column impact analysis completed for columnId: {}", columnId);
            return respVO;

        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error analyzing column impact for columnId: {}", columnId, e);
            return new ImpactAnalysisRespVO();
        }
    }

    @Override
    public ImpactAnalysisRespVO analyzeTableDeletionImpact(Long tableId) {
        return analyzeTableImpact(tableId, "DELETE");
    }

    @Override
    public ImpactAnalysisRespVO analyzeColumnDeletionImpact(Long columnId) {
        return analyzeColumnImpact(columnId, "DELETE");
    }

    @Override
    public ImpactAnalysisRespVO analyzeTableRenameImpact(Long tableId, String newName) {
        ImpactAnalysisRespVO respVO = analyzeTableImpact(tableId, "RENAME");
        
        // 添加重命名特定的分析信息
        Map<String, Object> renameDetails = new HashMap<>();
        renameDetails.put("newName", newName);
        renameDetails.put("renameImpact", "需要更新所有引用该表的查询和应用程序");
        
        Map<String, Object> analysisDetails = respVO.getAnalysisDetails();
        if (analysisDetails == null) {
            analysisDetails = new HashMap<>();
        }
        analysisDetails.put("renameDetails", renameDetails);
        respVO.setAnalysisDetails(analysisDetails);
        
        return respVO;
    }

    @Override
    public ImpactAnalysisRespVO analyzeColumnRenameImpact(Long columnId, String newName) {
        ImpactAnalysisRespVO respVO = analyzeColumnImpact(columnId, "RENAME");
        
        // 添加重命名特定的分析信息
        Map<String, Object> renameDetails = new HashMap<>();
        renameDetails.put("newName", newName);
        renameDetails.put("renameImpact", "需要更新所有引用该列的查询和应用程序");
        
        Map<String, Object> analysisDetails = respVO.getAnalysisDetails();
        if (analysisDetails == null) {
            analysisDetails = new HashMap<>();
        }
        analysisDetails.put("renameDetails", renameDetails);
        respVO.setAnalysisDetails(analysisDetails);
        
        return respVO;
    }

    @Override
    public ImpactAnalysisRespVO analyzeColumnTypeChangeImpact(Long columnId, String newType) {
        ImpactAnalysisRespVO respVO = analyzeColumnImpact(columnId, "TYPE_CHANGE");
        
        // 添加类型变更特定的分析信息
        Map<String, Object> typeChangeDetails = new HashMap<>();
        typeChangeDetails.put("newType", newType);
        typeChangeDetails.put("typeChangeRisk", calculateTypeChangeRisk(columnId, newType));
        
        Map<String, Object> analysisDetails = respVO.getAnalysisDetails();
        if (analysisDetails == null) {
            analysisDetails = new HashMap<>();
        }
        analysisDetails.put("typeChangeDetails", typeChangeDetails);
        respVO.setAnalysisDetails(analysisDetails);
        
        return respVO;
    }

    @Override
    public List<LineageNodeVO> getImpactedDownstreamTables(Long tableId, Integer depth) {
        log.info("[ImpactAnalyzer] Getting impacted downstream tables for tableId: {}, depth: {}", tableId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return lineageAnalyzer.getDownstreamTables(tableId, depth);
        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error getting impacted downstream tables for tableId: {}", tableId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LineageNodeVO> getImpactedDownstreamColumns(Long columnId, Integer depth) {
        log.info("[ImpactAnalyzer] Getting impacted downstream columns for columnId: {}, depth: {}", columnId, depth);

        try {
            depth = Math.min(depth, MAX_DEPTH);
            return lineageAnalyzer.getDownstreamColumns(columnId, depth);
        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error getting impacted downstream columns for columnId: {}", columnId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Integer calculateImpactScore(Long tableId) {
        try {
            List<LineageNodeVO> downstreamTables = getImpactedDownstreamTables(tableId, DEFAULT_DEPTH);
            
            int baseScore = Math.min(downstreamTables.size() * 10, 80); // 基础分数，最高80分
            int depthPenalty = 0;
            
            // 根据影响深度调整分数
            for (LineageNodeVO node : downstreamTables) {
                if (node.getLineageLevel() != null && node.getLineageLevel() > 2) {
                    depthPenalty += 5;
                }
            }
            
            return Math.min(100, baseScore + depthPenalty);
        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error calculating impact score for tableId: {}", tableId, e);
            return 0;
        }
    }

    @Override
    public Integer calculateColumnImpactScore(Long columnId) {
        try {
            List<LineageNodeVO> downstreamColumns = getImpactedDownstreamColumns(columnId, DEFAULT_DEPTH);
            
            int baseScore = Math.min(downstreamColumns.size() * 15, 85); // 列级影响分数稍高
            int depthPenalty = 0;
            
            // 根据影响深度调整分数
            for (LineageNodeVO node : downstreamColumns) {
                if (node.getLineageLevel() != null && node.getLineageLevel() > 2) {
                    depthPenalty += 3;
                }
            }
            
            return Math.min(100, baseScore + depthPenalty);
        } catch (Exception e) {
            log.error("[ImpactAnalyzer] Error calculating column impact score for columnId: {}", columnId, e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getImpactStatistics(Long tableId) {
        Map<String, Object> statistics = new HashMap<>();
        
        List<LineageNodeVO> directImpacted = getImpactedDownstreamTables(tableId, 1);
        List<LineageNodeVO> allImpacted = getImpactedDownstreamTables(tableId, DEFAULT_DEPTH);
        
        statistics.put("directImpactCount", directImpacted.size());
        statistics.put("totalImpactCount", allImpacted.size());
        statistics.put("indirectImpactCount", allImpacted.size() - directImpacted.size());
        statistics.put("impactScore", calculateImpactScore(tableId));
        
        return statistics;
    }

    @Override
    public Map<String, Object> getDatabaseImpactStatistics(Long databaseId) {
        // 简化实现，可以后续完善
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("databaseId", databaseId);
        statistics.put("totalTables", 0);
        statistics.put("highImpactTables", 0);
        return statistics;
    }

    @Override
    public String predictChangeRiskLevel(Long tableId, String changeType) {
        Integer impactScore = calculateImpactScore(tableId);
        
        // 根据变更类型和影响分数预测风险等级
        switch (changeType.toUpperCase()) {
            case "DELETE":
                if (impactScore >= 80) return "CRITICAL";
                if (impactScore >= 60) return "HIGH";
                if (impactScore >= 30) return "MEDIUM";
                return "LOW";
            case "RENAME":
                if (impactScore >= 70) return "HIGH";
                if (impactScore >= 40) return "MEDIUM";
                return "LOW";
            case "UPDATE":
                if (impactScore >= 60) return "MEDIUM";
                return "LOW";
            default:
                return "LOW";
        }
    }

    @Override
    public List<String> getChangeRecommendations(Long tableId, String changeType) {
        List<String> recommendations = new ArrayList<>();
        
        switch (changeType.toUpperCase()) {
            case "DELETE":
                recommendations.add("在删除表之前，请确保所有依赖的应用程序已经更新");
                recommendations.add("建议先备份相关数据");
                recommendations.add("考虑在低峰时段执行删除操作");
                break;
            case "RENAME":
                recommendations.add("更新所有引用该表的查询和存储过程");
                recommendations.add("通知相关开发团队更新应用程序代码");
                recommendations.add("考虑使用视图或别名来保持向后兼容性");
                break;
            case "UPDATE":
                recommendations.add("评估结构变更对现有数据的影响");
                recommendations.add("准备数据迁移脚本");
                recommendations.add("在测试环境中充分验证变更");
                break;
            default:
                recommendations.add("请仔细评估变更的影响范围");
                recommendations.add("建议在测试环境中先行验证");
        }
        
        return recommendations;
    }

    // 私有辅助方法
    private String generateAnalysisId(Long objectId, String changeType) {
        return String.format("impact_%d_%s_%d", objectId, changeType.toLowerCase(), System.currentTimeMillis());
    }

    private ImpactAnalysisRespVO.TargetObject buildTargetObject(MetaTableDO table) {
        ImpactAnalysisRespVO.TargetObject targetObject = new ImpactAnalysisRespVO.TargetObject();
        targetObject.setObjectId(table.getId());
        targetObject.setObjectType("TABLE");
        targetObject.setObjectName(table.getTableName());
        targetObject.setObjectDescription(table.getTableComment());
        targetObject.setDatabaseId(table.getDbId());
        return targetObject;
    }

    private ImpactAnalysisRespVO.TargetObject buildColumnTargetObject(MetaColumnDO column) {
        ImpactAnalysisRespVO.TargetObject targetObject = new ImpactAnalysisRespVO.TargetObject();
        targetObject.setObjectId(column.getId());
        targetObject.setObjectType("COLUMN");
        targetObject.setObjectName(column.getColumnName());
        targetObject.setObjectDescription(column.getColumnComment());
        return targetObject;
    }

    private ImpactAnalysisRespVO.ImpactStatistics buildImpactStatistics(List<LineageNodeVO> directImpacted, List<LineageNodeVO> indirectImpacted) {
        ImpactAnalysisRespVO.ImpactStatistics statistics = new ImpactAnalysisRespVO.ImpactStatistics();
        statistics.setDirectImpactCount(directImpacted.size());
        statistics.setIndirectImpactCount(indirectImpacted.size());
        statistics.setTotalImpactCount(directImpacted.size() + indirectImpacted.size());
        
        // 计算影响深度和广度
        int maxDepth = Math.max(
                directImpacted.stream().mapToInt(node -> node.getLineageLevel() != null ? node.getLineageLevel() : 1).max().orElse(1),
                indirectImpacted.stream().mapToInt(node -> node.getLineageLevel() != null ? node.getLineageLevel() : 1).max().orElse(1)
        );
        statistics.setImpactDepth(maxDepth);
        statistics.setImpactBreadth(directImpacted.size() + indirectImpacted.size());
        
        return statistics;
    }

    private ImpactAnalysisRespVO.ImpactStatistics buildColumnImpactStatistics(List<LineageNodeVO> directImpacted, List<LineageNodeVO> indirectImpacted) {
        ImpactAnalysisRespVO.ImpactStatistics statistics = buildImpactStatistics(directImpacted, indirectImpacted);
        statistics.setImpactedColumnCount(directImpacted.size() + indirectImpacted.size());
        return statistics;
    }

    private String calculateImpactLevel(Integer impactScore) {
        if (impactScore >= 80) return "CRITICAL";
        if (impactScore >= 60) return "HIGH";
        if (impactScore >= 30) return "MEDIUM";
        return "LOW";
    }

    private List<String> generateRiskMitigationActions(String changeType, Integer impactScore) {
        List<String> actions = new ArrayList<>();
        
        if (impactScore >= 60) {
            actions.add("制定详细的回滚计划");
            actions.add("准备应急响应团队");
            actions.add("设置监控告警");
        }
        
        if (impactScore >= 80) {
            actions.add("获得高级管理层批准");
            actions.add("通知所有相关利益方");
            actions.add("准备通信计划");
        }
        
        return actions;
    }

    private ImpactAnalysisRespVO.TimeWindow analyzeOptimalChangeWindow(Long tableId) {
        ImpactAnalysisRespVO.TimeWindow timeWindow = new ImpactAnalysisRespVO.TimeWindow();
        timeWindow.setSuggestedStartTime("2025-07-06 02:00:00");
        timeWindow.setSuggestedEndTime("2025-07-06 04:00:00");
        timeWindow.setWindowType("LOW_TRAFFIC");
        timeWindow.setWindowDescription("建议在低流量时段进行变更操作");
        timeWindow.setEstimatedImpactDuration(30);
        return timeWindow;
    }

    private Map<String, Object> buildAnalysisDetails(Long tableId, String changeType, List<LineageNodeVO> directImpacted, List<LineageNodeVO> indirectImpacted) {
        Map<String, Object> details = new HashMap<>();
        details.put("tableId", tableId);
        details.put("changeType", changeType);
        details.put("directImpactedCount", directImpacted.size());
        details.put("indirectImpactedCount", indirectImpacted.size());
        details.put("analysisDepth", DEFAULT_DEPTH);
        return details;
    }

    private String predictColumnChangeRiskLevel(Long columnId, String changeType) {
        Integer impactScore = calculateColumnImpactScore(columnId);
        return predictChangeRiskLevel(columnId, changeType); // 复用表级风险预测逻辑
    }

    private List<String> getColumnChangeRecommendations(Long columnId, String changeType) {
        return getChangeRecommendations(columnId, changeType); // 复用表级建议逻辑
    }

    private String calculateTypeChangeRisk(Long columnId, String newType) {
        // 简化实现，实际应该根据数据类型兼容性来判断
        return "MEDIUM";
    }

    // 其他接口方法的简化实现
    @Override
    public Map<String, Object> analyzeBatchChangesImpact(List<Map<String, Object>> changes) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> generateImpactReport(Long tableId) {
        return getImpactStatistics(tableId);
    }

    @Override
    public Map<String, Object> analyzeChangeTimeWindow(Long tableId) {
        Map<String, Object> timeWindow = new HashMap<>();
        timeWindow.put("optimalWindow", analyzeOptimalChangeWindow(tableId));
        return timeWindow;
    }

    @Override
    public Map<String, Object> getImpactChainAnalysis(Long tableId, Integer depth) {
        Map<String, Object> chainAnalysis = new HashMap<>();
        chainAnalysis.put("impactChain", getImpactedDownstreamTables(tableId, depth));
        return chainAnalysis;
    }

    @Override
    public Map<String, Object> validateImpactAnalysis(Long tableId) {
        Map<String, Object> validation = new HashMap<>();
        validation.put("isValid", true);
        validation.put("validationScore", 95);
        return validation;
    }
}
